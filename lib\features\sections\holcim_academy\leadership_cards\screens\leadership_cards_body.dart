import 'package:flutter/material.dart';
import 'dart:math' as math;

class LeadershipProgramsPage extends StatefulWidget {
  final String program;
  const LeadershipProgramsPage({super.key, required this.program});

  @override
  State<LeadershipProgramsPage> createState() => _LeadershipProgramsPageState();
}

class _LeadershipProgramsPageState extends State<LeadershipProgramsPage>
    with TickerProviderStateMixin {
  final List<ProgramItem> programs = [
    ProgramItem(
      id: 1,
      title: "Business School for Advanced Leaders",
      focusAreas: "Executing in a changing environment and engaging employees",
      highlights:
          "Interactive virtual sessions led by Ivey School of Business faculty, covering topics like green growth, organizational change, diversity, and leadership narratives.",
      audience:
          "Advanced leaders responsible for significant organizational areas",
      format: "Virtual",
      color: Colors.blue.shade800,
      icon: Icons.school,
    ),
    ProgramItem(
      id: 2,
      title: "Business School for Emerging Leaders",
      focusAreas: "Leadership, strategy, and business acumen",
      highlights:
          "Includes FranklinCovey's \"The 4 Essential Roles of Leadership,\" Harvard simulations, strategic tools, financial understanding, and 1:1 coaching.",
      audience: "Mid-level leaders nominated for the program",
      format: "4 months modular program",
      color: Colors.blue.shade600,
      icon: Icons.trending_up,
    ),
    ProgramItem(
      id: 3,
      title: "Early Career Leadership Program",
      focusAreas:
          "Leadership development, business skills, and exposure to senior management",
      highlights:
          "Virtual program featuring FranklinCovey leadership training, business simulations, keynote webinars, and coaching.",
      audience: "Early career leaders nominated for the program",
      format: "6 months",
      color: Colors.blue.shade400,
      icon: Icons.rocket_launch,
    ),
    // ProgramItem(
    //   id: 4,
    //   title: "The 4 Essential Roles of Leadership",
    //   focusAreas: "Leadership fundamentals",
    //   highlights:
    //       "A series of webinars focusing on essential leadership roles, available as part of other programs or standalone.",
    //   audience: "Leaders at various levels",
    //   format: "Webinar series",
    //   color: Colors.green.shade700,
    //   icon: Icons.people,
    // ),
    // ProgramItem(
    //   id: 5,
    //   title: "Coaching",
    //   focusAreas: "Personal and professional development",
    //   highlights:
    //       "Virtual 1:1 coaching sessions with certified business coaches available in over 60 languages.",
    //   audience: "Training participants, departments, or individuals",
    //   format: "1:1 Sessions",
    //   color: Colors.green.shade500,
    //   icon: Icons.psychology,
    // ),
    // ProgramItem(
    //   id: 6,
    //   title: "Management Essentials",
    //   focusAreas:
    //       "Workshops on cognitive diversity, intercultural awareness, and communication skills",
    //   highlights:
    //       "Includes Percipio journeys for first-time and mid-level managers.",
    //   audience: "Managers at different levels",
    //   format: "Workshops",
    //   color: Colors.green.shade300,
    //   icon: Icons.settings,
    // ),
    // ProgramItem(
    //   id: 7,
    //   title: "Sustainable Development",
    //   focusAreas:
    //       "Environmental and social challenges, net-zero goals, circular economy, biodiversity, and human rights",
    //   highlights:
    //       "Percipio channel with learning blocks on sustainability topics.",
    //   audience: "All employees passionate about sustainability",
    //   format: "Self-paced learning",
    //   color: const Color(0xFF0F3A4D),
    //   icon: Icons.eco,
    // ),
  ];

  int _selectedIndex = 0;
  late final AnimationController _backgroundController;
  late final AnimationController _cardAnimationController;
  late final AnimationController _pageTransitionController;
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 30000),
      vsync: this,
    )..repeat();

    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    )..forward();

    _pageTransitionController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pageController = PageController(
      initialPage: programs.indexWhere((p) => p.title == widget.program),
      viewportFraction: 0.85,
    );
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _cardAnimationController.dispose();
    _pageTransitionController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _selectedIndex = index;
      _pageTransitionController.reset();
      _pageTransitionController.forward();
    });
  }

  @override
  Widget build(BuildContext context) {
    // We'll keep MediaQuery for potential future use
    return Stack(
      children: [
        // Background Animation
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _backgroundController,
            builder: (context, child) {
              return CustomPaint(
                painter: BackgroundPainter(
                  animation: _backgroundController.value,
                  color1: const Color(
                    0xFF0F3A4D,
                  ).withAlpha(13), // Replaced withOpacity with withAlpha
                  color2: Colors.blue.withAlpha(
                    13,
                  ), // Replaced withOpacity with withAlpha
                  color3: Colors.green.withAlpha(
                    13,
                  ), // Replaced withOpacity with withAlpha
                ),
                size: Size.infinite,
              );
            },
          ),
        ),

        // Main Content
        Column(
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(48, 48, 48, 16),
              child: Row(
                children: [
                  Text(
                    'HOLCIM',
                    style: TextStyle(
                      color: const Color(0xFF0F3A4D),
                      fontWeight: FontWeight.w900,
                      fontSize: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'Leadership & Management Development',
                    style: TextStyle(
                      color: const Color(0xFF0F3A4D),
                      fontWeight: FontWeight.w600,
                      fontSize: 24,
                    ),
                  ),
                  const Spacer(),
                  ActionButton(icon: Icons.info_outline, onPressed: () {}),
                ],
              ),
            ),
            // Program Tabs
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 48),
              child: SizedBox(
                height: 60,
                child: ProgramTabBar(
                  programs: programs,
                  selectedIndex: _selectedIndex,
                  onTap: (index) {
                    setState(() {
                      _selectedIndex = index;
                      _pageController.animateToPage(
                        index,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeInOut,
                      );
                      _pageTransitionController.reset();
                      _pageTransitionController.forward();
                    });
                  },
                ),
              ),
            ),
            const SizedBox(height: 32),
            // Program Content
            Expanded(
              child: GestureDetector(
                // Add explicit gesture detector to ensure gestures are captured
                onHorizontalDragEnd: (details) {
                  // Handle swipe gestures
                  if (details.primaryVelocity != null) {
                    if (details.primaryVelocity! < -500) {
                      // Swipe left - go to next page
                      if (_selectedIndex < programs.length - 1) {
                        _pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    } else if (details.primaryVelocity! > 500) {
                      // Swipe right - go to previous page
                      if (_selectedIndex > 0) {
                        _pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    }
                  }
                },
                child: PageView.builder(
                  controller: _pageController,
                  scrollDirection: Axis.horizontal,
                  // Use ClampingScrollPhysics for better scrolling behavior
                  physics: const ClampingScrollPhysics(),
                  itemCount: programs.length,
                  onPageChanged: _onPageChanged,
                  pageSnapping: true, // Ensure pages snap into place
                  allowImplicitScrolling: true, // Allow smoother scrolling
                  itemBuilder: (context, index) {
                    return AnimatedBuilder(
                      animation: _pageTransitionController,
                      builder: (context, child) {
                        final isActive = index == _selectedIndex;
                        return Opacity(
                          opacity: isActive ? 1.0 : 0.7,
                          child: Transform.scale(
                            scale: isActive ? 1.0 : 0.95,
                            child: child,
                          ),
                        );
                      },
                      child: ProgramCard(program: programs[index]),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 48),
            // Bottom Indicators
            Padding(
              padding: const EdgeInsets.only(bottom: 32),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  programs.length,
                  (index) => Container(
                    width: 12,
                    height: 12,
                    margin: const EdgeInsets.symmetric(horizontal: 6),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          _selectedIndex == index
                              ? programs[index].color
                              : Colors.grey.shade300,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class ProgramTabBar extends StatefulWidget {
  final List<ProgramItem> programs;
  final int selectedIndex;
  final Function(int) onTap;

  const ProgramTabBar({
    super.key,
    required this.programs,
    required this.selectedIndex,
    required this.onTap,
  });

  @override
  State<ProgramTabBar> createState() => _ProgramTabBarState();
}

class _ProgramTabBarState extends State<ProgramTabBar> {
  final ScrollController _scrollController = ScrollController();
  final List<GlobalKey> _tabKeys = [];
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initTabKeys();

    // Add listener to scroll controller to handle manual scrolling
    _scrollController.addListener(_handleScroll);

    // Schedule initial scroll after the first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _isInitialized = true;
      _scrollToSelectedTab();
    });
  }

  void _initTabKeys() {
    _tabKeys.clear();
    for (int i = 0; i < widget.programs.length; i++) {
      _tabKeys.add(GlobalKey());
    }
  }

  @override
  void didUpdateWidget(ProgramTabBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Add keys if the number of programs changed
    if (oldWidget.programs.length != widget.programs.length) {
      _initTabKeys();
    }

    // Scroll to the selected tab when it changes
    if (oldWidget.selectedIndex != widget.selectedIndex && _isInitialized) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToSelectedTab();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _handleScroll() {
    // This helps ensure the scroll controller is responsive
    setState(() {});
  }

  void _scrollToSelectedTab() {
    if (!_scrollController.hasClients) return;
    if (widget.selectedIndex >= _tabKeys.length) return;

    try {
      // Get the context of the selected tab
      final context = _tabKeys[widget.selectedIndex].currentContext;
      if (context == null) return;

      // Get the render box of the selected tab
      final RenderBox renderBox = context.findRenderObject() as RenderBox;
      final size = renderBox.size;

      // Get the position of the tab in the viewport
      final position = renderBox.localToGlobal(Offset.zero);

      // Calculate the screen center
      final screenWidth = MediaQuery.of(context).size.width;
      final screenCenter = screenWidth / 2;

      // Calculate the offset to center the tab
      final tabCenter = position.dx + (size.width / 2);
      final offset = _scrollController.offset + (tabCenter - screenCenter);

      // Ensure we don't scroll beyond bounds
      final maxScroll = _scrollController.position.maxScrollExtent;
      final scrollTo = offset.clamp(0.0, maxScroll);

      // Animate to the position
      _scrollController.animateTo(
        scrollTo,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
      );
    } catch (e) {
      // Handle any errors that might occur during scrolling
      // Silently handle the error
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      physics: const ClampingScrollPhysics(), // Better scrolling physics
      itemCount: widget.programs.length,
      itemBuilder: (context, index) {
        final program = widget.programs[index];
        final isSelected = index == widget.selectedIndex;

        return Container(
          key: _tabKeys[index], // Assign key to measure position
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => widget.onTap(index),
              borderRadius: BorderRadius.circular(16),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 8),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? program.color : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color:
                          isSelected
                              ? Colors.black.withAlpha(
                                13,
                              ) // Replaced withOpacity with withAlpha
                              : Colors.transparent,
                      blurRadius: isSelected ? 12 : 6,
                      offset: Offset(0, isSelected ? 4 : 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize:
                      MainAxisSize.min, // Ensure tab width fits content
                  children: [
                    Icon(
                      program.icon,
                      color: isSelected ? Colors.white : program.color,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      program.title,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey.shade800,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class ActionButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;

  const ActionButton({super.key, required this.icon, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(
              13,
            ), // Replaced withOpacity with withAlpha
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(icon, color: const Color(0xFF0F3A4D)),
        onPressed: onPressed,
        iconSize: 24,
        padding: const EdgeInsets.all(12),
      ),
    );
  }
}

class BackgroundPainter extends CustomPainter {
  final double animation;
  final Color color1;
  final Color color2;
  final Color color3;

  BackgroundPainter({
    required this.animation,
    required this.color1,
    required this.color2,
    required this.color3,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Draw first blob
    final path1 = Path();
    final centerX1 = size.width * 0.2;
    final centerY1 = size.height * 0.3;
    final radius1 = size.width * 0.25;

    for (int i = 0; i < 360; i += 5) {
      final angle = i * math.pi / 180;
      final offset = math.sin(angle * 8 + animation * math.pi * 2) * 30;
      final x = centerX1 + (radius1 + offset) * math.cos(angle);
      final y = centerY1 + (radius1 + offset) * math.sin(angle);

      if (i == 0) {
        path1.moveTo(x, y);
      } else {
        path1.lineTo(x, y);
      }
    }
    path1.close();
    paint.color = color1;
    canvas.drawPath(path1, paint);

    // Draw second blob
    final path2 = Path();
    final centerX2 = size.width * 0.8;
    final centerY2 = size.height * 0.4;
    final radius2 = size.width * 0.2;

    for (int i = 0; i < 360; i += 5) {
      final angle = i * math.pi / 180;
      final offset =
          math.sin(angle * 6 + animation * math.pi * 2 + math.pi) * 25;
      final x = centerX2 + (radius2 + offset) * math.cos(angle);
      final y = centerY2 + (radius2 + offset) * math.sin(angle);

      if (i == 0) {
        path2.moveTo(x, y);
      } else {
        path2.lineTo(x, y);
      }
    }
    path2.close();
    paint.color = color2;
    canvas.drawPath(path2, paint);

    // Draw third blob
    final path3 = Path();
    final centerX3 = size.width * 0.5;
    final centerY3 = size.height * 0.7;
    final radius3 = size.width * 0.3;

    for (int i = 0; i < 360; i += 5) {
      final angle = i * math.pi / 180;
      final offset =
          math.sin(angle * 4 + animation * math.pi * 2 + math.pi * 0.5) * 35;
      final x = centerX3 + (radius3 + offset) * math.cos(angle);
      final y = centerY3 + (radius3 + offset) * math.sin(angle);

      if (i == 0) {
        path3.moveTo(x, y);
      } else {
        path3.lineTo(x, y);
      }
    }
    path3.close();
    paint.color = color3;
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(BackgroundPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

class ProgramItem {
  final int id;
  final String title;
  final String focusAreas;
  final String highlights;
  final String audience;
  final String format;
  final Color color;
  final IconData icon;

  ProgramItem({
    required this.id,
    required this.title,
    required this.focusAreas,
    required this.highlights,
    required this.audience,
    required this.format,
    required this.color,
    required this.icon,
  });
}

class ProgramCard extends StatelessWidget {
  final ProgramItem program;

  const ProgramCard({super.key, required this.program});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: program.color.withAlpha(
              51,
            ), // Replaced withOpacity with withAlpha
            blurRadius: 30,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(32),
        child: Stack(
          children: [
            // Program content
            SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(30.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: program.color.withAlpha(
                              26,
                            ), // Replaced withOpacity with withAlpha
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Icon(
                            program.icon,
                            color: program.color,
                            size: 48,
                          ),
                        ),
                        const SizedBox(width: 26),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                program.title,
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF0F3A4D),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: program.color.withAlpha(
                                        26,
                                      ), // Replaced withOpacity with withAlpha
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      "Program #${program.id}",
                                      style: TextStyle(
                                        color: program.color,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: program.color.withAlpha(
                                        26,
                                      ), // Replaced withOpacity with withAlpha
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      program.format,
                                      style: TextStyle(
                                        color: program.color,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                    // Focus Areas
                    Text(
                      'Focus Areas',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0F3A4D),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Text(
                        program.focusAreas,
                        style: TextStyle(
                          fontSize: 18,
                          height: 1.5,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Program Highlights
                    Text(
                      'Program Highlights',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0F3A4D),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Text(
                        program.highlights,
                        style: TextStyle(
                          fontSize: 18,
                          height: 1.5,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Target Audience
                    Text(
                      'Target Audience',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0F3A4D),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Text(
                        program.audience,
                        style: TextStyle(
                          fontSize: 18,
                          height: 1.5,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Decorative corner
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: program.color.withAlpha(
                    26,
                  ), // Replaced withOpacity with withAlpha
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(120),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 24, left: 24),
                  child: Align(
                    alignment: Alignment.topRight,
                    child: Icon(
                      Icons.arrow_forward,
                      color: program.color,
                      size: 32,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
