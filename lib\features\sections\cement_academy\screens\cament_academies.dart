import 'package:cilas_biskra/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;

class CementAcademiesBody extends StatefulWidget {
  const CementAcademiesBody({super.key});

  @override
  State<CementAcademiesBody> createState() => _CementAcademiesBodyState();
}

class _CementAcademiesBodyState extends State<CementAcademiesBody>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _backgroundController;
  late AnimationController _gridController;
  late AnimationController _floatingController;
  late AnimationController _tapController;

  late Animation<double> _headerSlideAnimation;
  late Animation<double> _headerFadeAnimation;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _gridStaggerAnimation;
  late Animation<double> _tapAnimation;

  bool _isDisposed = false;

  final List<Academy> academies = [
    Academy(
      title: 'Project Management',
      subtitle: 'Strategic Leadership & Planning',
      icon: Icons.engineering,
      gradient: const LinearGradient(
        colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Process & Production',
      subtitle: 'Manufacturing Excellence',
      icon: Icons.precision_manufacturing,
      gradient: const LinearGradient(
        colors: [Color(0xFFFF6B6B), Color(0xFFEE5A24)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Plant & Leadership',
      subtitle: 'Operational Management',
      icon: Icons.factory,
      gradient: const LinearGradient(
        colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Quarry',
      subtitle: 'Mining & Extraction',
      icon: Icons.terrain,
      gradient: const LinearGradient(
        colors: [Color(0xFFFFA726), Color(0xFFFF7043)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Maintainence',
      subtitle: 'Technical Operations',
      icon: Icons.build_circle,
      gradient: const LinearGradient(
        colors: [Color(0xFF9C27B0), Color(0xFF673AB7)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Quality & Environment',
      subtitle: 'Standards & Sustainability',
      icon: Icons.eco,
      gradient: const LinearGradient(
        colors: [Color(0xFF66BB6A), Color(0xFF43A047)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _gridController = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );

    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _tapController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _headerSlideAnimation = Tween<double>(begin: -100, end: 0).animate(
      CurvedAnimation(parent: _headerController, curve: Curves.elasticOut),
    );

    _headerFadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _headerController, curve: Curves.easeInOut),
    );

    _backgroundAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _backgroundController, curve: Curves.easeInOut),
    );

    _gridStaggerAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _gridController, curve: Curves.easeOutCubic),
    );

    _tapAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _tapController, curve: Curves.easeInOut));
  }

  void _startAnimationSequence() async {
    if (_isDisposed) return;

    _floatingController.repeat(reverse: true);
    _backgroundController.forward();

    await Future.delayed(const Duration(milliseconds: 300));
    if (_isDisposed) return;

    _headerController.forward();

    await Future.delayed(const Duration(milliseconds: 500));
    if (_isDisposed) return;

    _gridController.forward();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _headerController.dispose();
    _backgroundController.dispose();
    _gridController.dispose();
    _floatingController.dispose();
    _tapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          _buildAnimatedBackground(),
          CustomScrollView(
            slivers: [
              _buildAnimatedHeader(),
              SliverToBoxAdapter(child: _buildResponsiveGrid()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF0F0F23).withOpacity(0.9),
                const Color(0xFF1A1A2E).withOpacity(0.8),
                const Color(0xFF16213E).withOpacity(0.7),
              ],
              stops: [
                0.0,
                _backgroundAnimation.value * 0.5,
                _backgroundAnimation.value,
              ],
            ),
          ),
          child: AnimatedBuilder(
            animation: _floatingController,
            builder: (context, child) {
              return CustomPaint(
                painter: FloatingParticlesPainter(_floatingController.value),
                size: Size.infinite,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildAnimatedHeader() {
    return SliverAppBar(
      expandedHeight: 300,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        background: AnimatedBuilder(
          animation: _headerController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, _headerSlideAnimation.value),
              child: Opacity(
                opacity: _headerFadeAnimation.value,
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Colors.transparent, Color(0xFF0F0F23)],
                    ),
                  ),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Holcim Functional',
                          style: TextStyle(
                            fontSize: 48,
                            fontWeight: FontWeight.w300,
                            color: Colors.white70,
                            letterSpacing: 2,
                          ),
                        ),
                        Text(
                          'ACADEMIES',
                          style: TextStyle(
                            fontSize: 64,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            letterSpacing: 8,
                            height: 0.8,
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Excellence in Cement Manufacturing',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            color: Colors.white60,
                            letterSpacing: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildResponsiveGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;

        // Responsive grid configuration
        int crossAxisCount;
        double childAspectRatio;
        double spacing;

        if (screenWidth > 2400) {
          // 24+ inch screens
          crossAxisCount = 4;
          childAspectRatio = 0.9;
          spacing = 24;
        } else if (screenWidth > 1920) {
          // Large desktops
          crossAxisCount = 3;
          childAspectRatio = 0.85;
          spacing = 20;
        } else if (screenWidth > 1200) {
          // Medium desktops
          crossAxisCount = 3;
          childAspectRatio = 0.8;
          spacing = 16;
        } else {
          // Small screens
          crossAxisCount = 2;
          childAspectRatio = 0.9;
          spacing = 12;
        }

        return AnimatedBuilder(
          animation: _gridStaggerAnimation,
          builder: (context, child) {
            return Padding(
              padding: EdgeInsets.all(screenWidth > 1920 ? 64 : 32),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: childAspectRatio,
                  crossAxisSpacing: spacing,
                  mainAxisSpacing: spacing,
                ),
                itemCount: academies.length,
                itemBuilder: (context, index) {
                  final delay = index * 0.1;
                  final animationValue = (_gridStaggerAnimation.value - delay)
                      .clamp(0.0, 1.0);

                  return Transform.scale(
                    scale: Curves.elasticOut.transform(animationValue),
                    child: Opacity(
                      opacity: animationValue,
                      child: ModernAcademyCard(
                        academy: academies[index],
                        onTap:
                            () => _handleAcademyTap(
                              context,
                              academies[index].title,
                            ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  void _handleAcademyTap(BuildContext context, String title) {
    // Add tap animation
    _tapController.forward().then((_) {
      _tapController.reverse();
    });

    // Navigate based on academy title
    switch (title) {
      case 'Process & Production':
        context.go('${AppRoute.cementDetails}/process-production');
        break;
      case 'Quarry':
        context.go('${AppRoute.cementDetails}/quarry');
        break;
      case 'Quality & Environment':
        context.go('${AppRoute.cementDetails}/quality-environment');
        break;
      case 'Maintainence':
        context.go('${AppRoute.cementDetails}/maintenance');
        break;
      case 'Project Management':
        context.go('${AppRoute.cementDetails}/project-management');
        break;
      case 'Plant & Leadership':
        context.go('${AppRoute.cementDetails}/plant-leadership');
        break;
      default:
        context.go(AppRoute.cementDetails);
        break;
    }
  }
}

class Academy {
  final String title;
  final String subtitle;
  final IconData icon;
  final LinearGradient gradient;

  Academy({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.gradient,
  });
}

class ModernAcademyCard extends StatefulWidget {
  final Academy academy;
  final VoidCallback onTap;

  const ModernAcademyCard({
    super.key,
    required this.academy,
    required this.onTap,
  });

  @override
  State<ModernAcademyCard> createState() => _ModernAcademyCardState();
}

class _ModernAcademyCardState extends State<ModernAcademyCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _tapController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _tapController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _tapController, curve: Curves.easeInOut));

    _elevationAnimation = Tween<double>(
      begin: 8.0,
      end: 4.0,
    ).animate(CurvedAnimation(parent: _tapController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _tapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _tapController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: _elevationAnimation.value,
                  offset: Offset(0, _elevationAnimation.value / 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  // Add tap feedback animation
                  _tapController.forward().then((_) {
                    _tapController.reverse();
                  });
                  widget.onTap();
                },
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: widget.academy.gradient,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Stack(
                    children: [
                      // Background pattern
                      Positioned.fill(
                        child: CustomPaint(painter: CardPatternPainter(0.3)),
                      ),
                      // Content
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              widget.academy.icon,
                              size: 40,
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                            const Spacer(),
                            Text(
                              widget.academy.title,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                                height: 1.2,
                              ),
                            ),
                            const SizedBox(height: 6),
                            Text(
                              widget.academy.subtitle,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.white.withValues(alpha: 0.8),
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Container(
                              width: 40,
                              height: 3,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.8),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class FloatingParticlesPainter extends CustomPainter {
  final double animationValue;

  FloatingParticlesPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.fill;

    for (int i = 0; i < 20; i++) {
      final x =
          (size.width / 20) * i +
          (math.sin(animationValue * 2 * math.pi + i) * 50);
      final y =
          (size.height / 4) +
          (math.cos(animationValue * 2 * math.pi + i * 0.5) * 100);
      final radius = 2 + (math.sin(animationValue * 2 * math.pi + i * 2) * 2);

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class CardPatternPainter extends CustomPainter {
  final double animationValue;

  CardPatternPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1 * animationValue)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    final path = Path();
    path.moveTo(size.width * 0.8, 0);
    path.quadraticBezierTo(
      size.width,
      size.height * 0.2,
      size.width,
      size.height * 0.5,
    );
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.8,
      size.width * 0.6,
      size.height,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
