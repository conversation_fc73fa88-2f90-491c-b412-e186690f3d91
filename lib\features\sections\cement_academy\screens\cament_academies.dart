import 'package:cilas_biskra/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CamentAcademiesBody extends StatefulWidget {
  const CamentAcademiesBody({super.key});

  @override
  State<CamentAcademiesBody> createState() => _CamentAcademiesBodyState();
}

class _CamentAcademiesBodyState extends State<CamentAcademiesBody>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _headerController;
  late List<AnimationController> _itemControllers;
  late Animation<double> _headerFadeAnimation;
  bool _isDisposed = false;

  final List<Color> colors = [
    const Color(0xFF6366F1), // Indigo
    const Color(0xFFEC4899), // Pink
    const Color(0xFF10B981), // Emerald
    const Color(0xFFF59E0B), // Amber
    const Color(0xFFEF4444), // Red
    const Color(0xFF8B5CF6), // Violet
    const Color(0xFF06B6D4), // Cyan
  ];

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _headerFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _headerController, curve: Curves.easeOut),
    );

    _itemControllers = List.generate(
      colors.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 600 + (index * 100)),
        vsync: this,
      ),
    );

    _startAnimations();
  }

  void _startAnimations() async {
    if (_isDisposed || !mounted) return;

    _headerController.forward();
    await Future.delayed(const Duration(milliseconds: 300));

    for (int i = 0; i < _itemControllers.length; i++) {
      if (_isDisposed || !mounted) return;

      await Future.delayed(const Duration(milliseconds: 80));

      if (_isDisposed || !mounted) return;
      _itemControllers[i].forward();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _controller.dispose();
    _headerController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 200,
          floating: false,
          pinned: true,
          elevation: 0,
          backgroundColor: const Color(0xFF0F0F23),
          flexibleSpace: FlexibleSpaceBar(
            centerTitle: true,
            title: AnimatedBuilder(
              animation: _headerFadeAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _headerFadeAnimation.value,
                  child: Transform.translate(
                    offset: Offset(0, 20 * (1 - _headerFadeAnimation.value)),
                    child: const Text(
                      'Holcim Functional Academies',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        letterSpacing: -0.5,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: _buildCoolArrangement(),
          ),
        ),
      ],
    );
  }

  // Helper method to calculate responsive image size
  double _getResponsiveImageSize(double screenWidth, double baseSize) {
    if (screenWidth < 1200) {
      // Small desktop: base size
      return baseSize;
    } else if (screenWidth < 1600) {
      // Medium desktop: larger images
      return baseSize * 1.2;
    } else {
      // Large desktop: much larger images
      return baseSize * 1.5;
    }
  }

  // Helper method to calculate responsive container height
  double _getResponsiveContainerHeight(double screenWidth) {
    if (screenWidth < 1200) {
      return 800; // Small desktop
    } else if (screenWidth < 1600) {
      return 1000; // Medium desktop
    } else if (screenWidth > 1600) {
      return 1200; // Large desktop
    }
    return 800;
  }

  Widget _buildCoolArrangement() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final baseImageSize = 250.0;
        final responsiveSize = _getResponsiveImageSize(width, baseImageSize);
        final containerHeight = _getResponsiveContainerHeight(width);

        return SizedBox(
          height: containerHeight,
          child: Stack(
            children: [
              // Large center image
              Positioned(
                left: width * 0.38,
                top: containerHeight * 0.25,
                child: _buildAnimatedItem(
                  0,
                  responsiveSize,
                  responsiveSize,
                  'Project Management',
                ),
              ),
              // Top left
              Positioned(
                left: width * 0.05,
                top: containerHeight * 0.06,
                child: _buildAnimatedItem(
                  1,
                  responsiveSize,
                  responsiveSize,
                  'Process & Production',
                ),
              ),
              // Top right
              Positioned(
                right: width * 0.05,
                top: containerHeight * 0.08,
                child: _buildAnimatedItem(
                  2,
                  responsiveSize,
                  responsiveSize,
                  'Plant & Leadership',
                ),
              ),
              // Bottom left
              Positioned(
                left: width * 0.1,
                bottom: containerHeight * 0.12,
                child: _buildAnimatedItem(
                  3,
                  responsiveSize,
                  responsiveSize,
                  'Quarry',
                ),
              ),
              // Bottom right
              Positioned(
                right: width * 0.33,
                bottom: containerHeight * 0.10,
                child: _buildAnimatedItem(
                  4,
                  responsiveSize,
                  responsiveSize,
                  'Maintainence',
                ),
              ),
              // Middle right
              Positioned(
                right: width * 0.05,
                top: containerHeight * 0.5,
                child: _buildAnimatedItem(
                  6,
                  responsiveSize,
                  responsiveSize,
                  'Quality & Environment',
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnimatedItem(
    int index,
    double width,
    double height,
    String title,
  ) {
    return AnimatedBuilder(
      animation: _itemControllers[index],
      builder: (context, child) {
        final animation = CurvedAnimation(
          parent: _itemControllers[index],
          curve: Curves.elasticOut,
        );
        return Transform.scale(
          scale: animation.value,
          child: Opacity(
            opacity: animation.value.clamp(0.0, 1.0),
            child: SizedBox(
              width: width,
              height: height,
              child: GalleryItem(
                color: colors[index],
                index: index,
                title: title,
              ),
            ),
          ),
        );
      },
    );
  }
}

class GalleryItem extends StatefulWidget {
  final Color color;
  final int index;
  final String title;

  const GalleryItem({
    super.key,
    required this.color,
    required this.index,
    required this.title,
  });

  @override
  State<GalleryItem> createState() => _GalleryItemState();
}

class _GalleryItemState extends State<GalleryItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _tapController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _tapController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(parent: _tapController, curve: Curves.easeOut));

    _elevationAnimation = Tween<double>(
      begin: 8.0,
      end: 20.0,
    ).animate(CurvedAnimation(parent: _tapController, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _tapController.dispose();
    super.dispose();
  }

  void _handleAcademyTap(BuildContext context, String title) {
    // Add tap animation
    _tapController.forward().then((_) {
      _tapController.reverse();
    });

    // Navigate based on academy title
    switch (title) {
      case 'Process & Production':
        context.go('${AppRoute.cementDetails}/process-production');
        break;
      case 'Quarry':
        context.go('${AppRoute.cementDetails}/quarry');
        break;
      case 'Quality & Environment':
        context.go('${AppRoute.cementDetails}/quality-environment');
        break;
      case 'Maintainence':
        context.go('${AppRoute.cementDetails}/maintenance');
        break;
      case 'Project Management':
        context.go('${AppRoute.plantLeadership}/project-management');
        break;
      case 'Plant & Leadership':
        context.go('${AppRoute.plantLeadership}/plant-leadership');
        break;
      default:
        // Fallback to general cement details
        context.go(AppRoute.cementDetails);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _tapController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: widget.color.withAlpha(76), // 0.3 * 255 = 76
                  blurRadius: _elevationAnimation.value,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: Colors.black.withAlpha(25), // 0.1 * 255 = 25
                  blurRadius: _elevationAnimation.value * 0.5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: GestureDetector(
                onTap: () {
                  _handleAcademyTap(context, widget.title);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.color,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      widget.title,
                      style: TextStyle(
                        color: Colors.white.withAlpha(229), // 0.9 * 255 = 229
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
