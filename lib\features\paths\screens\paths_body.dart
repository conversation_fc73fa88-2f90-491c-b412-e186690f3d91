import 'package:cilas_biskra/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class PathsBody extends StatefulWidget {
  const PathsBody({super.key});

  @override
  State<PathsBody> createState() => _PathsBodyState();
}

class _PathsBodyState extends State<PathsBody> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _rotateController;
  late AnimationController _pulseController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _rotateController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Setup animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
    );

    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 2 * 3.14159,
    ).animate(_rotateController);

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
    _rotateController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _rotateController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  // Responsive utilities for large screens
  double _getMaxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth > 1920 ? 1920 : screenWidth;
  }

  double _getResponsiveSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = (screenWidth / 1920).clamp(0.8, 1.5);
    return baseSize * scaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    final maxWidth = _getMaxWidth(context);

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF0F172A), Color(0xFF334155)],
        ),
      ),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            child: Center(
              child: Container(
                constraints: BoxConstraints(maxWidth: maxWidth),
                child: Column(
                  children: [
                    _buildModernAppBar(context),
                    _buildHeroSection(context),
                    _buildLearningPathsSection(context),
                    _buildFloatingFooter(context),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      height: _getResponsiveSize(context, 80),
      margin: EdgeInsets.all(_getResponsiveSize(context, 24)),
      padding: EdgeInsets.symmetric(
        horizontal: _getResponsiveSize(context, 32),
        vertical: _getResponsiveSize(context, 16),
      ),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(_getResponsiveSize(context, 20)),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          // Animated Logo
          AnimatedBuilder(
            animation: _rotateAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotateAnimation.value * 0.1,
                child: Container(
                  width: _getResponsiveSize(context, 48),
                  height: _getResponsiveSize(context, 48),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF06B6D4), Color(0xFF3B82F6)],
                    ),
                    borderRadius: BorderRadius.circular(
                      _getResponsiveSize(context, 12),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF06B6D4).withOpacity(0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      'H',
                      style: GoogleFonts.poppins(
                        fontSize: _getResponsiveSize(context, 24),
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          SizedBox(width: _getResponsiveSize(context, 16)),

          // Brand Name
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'Hol',
                  style: GoogleFonts.poppins(
                    fontSize: _getResponsiveSize(context, 24),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF06B6D4),
                  ),
                ),
                TextSpan(
                  text: 'Cim',
                  style: GoogleFonts.poppins(
                    fontSize: _getResponsiveSize(context, 24),
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Navigation
          Row(
            children: [
              _buildNavButton("Home", () => context.go(AppRoute.splash)),
              SizedBox(width: _getResponsiveSize(context, 24)),

              // Profile Avatar
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: _getResponsiveSize(context, 40),
                      height: _getResponsiveSize(context, 40),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF8B5CF6), Color(0xFFEC4899)],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF8B5CF6).withOpacity(0.4),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.account_circle_outlined,
                        color: Colors.white,
                        size: _getResponsiveSize(context, 20),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavButton(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: _getResponsiveSize(context, 20),
          vertical: _getResponsiveSize(context, 8),
        ),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(_getResponsiveSize(context, 8)),
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
        ),
        child: Text(
          label,
          style: GoogleFonts.inter(
            fontSize: _getResponsiveSize(context, 14),
            fontWeight: FontWeight.w500,
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: _getResponsiveSize(context, 24),
        vertical: _getResponsiveSize(context, 32),
      ),
      padding: EdgeInsets.all(_getResponsiveSize(context, 32)),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF06B6D4), Color(0xFF3B82F6), Color(0xFF8B5CF6)],
        ),
        borderRadius: BorderRadius.circular(_getResponsiveSize(context, 32)),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF06B6D4).withOpacity(0.3),
            blurRadius: 40,
            offset: const Offset(0, 20),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: _getResponsiveSize(context, 16),
                    vertical: _getResponsiveSize(context, 8),
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(
                      _getResponsiveSize(context, 20),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        color: Colors.white,
                        size: _getResponsiveSize(context, 16),
                      ),
                      SizedBox(width: _getResponsiveSize(context, 8)),
                      Text(
                        'LEARNING CORNER',
                        style: GoogleFonts.inter(
                          fontSize: _getResponsiveSize(context, 12),
                          fontWeight: FontWeight.w600,
                          letterSpacing: 1.0,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: _getResponsiveSize(context, 24)),

                Text(
                  'Building skills for\ntomorrow\'s world',
                  style: GoogleFonts.poppins(
                    fontSize: _getResponsiveSize(context, 48),
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    height: 1.1,
                  ),
                ),
                SizedBox(height: _getResponsiveSize(context, 16)),

                Text(
                  'Access world-class learning resources tailored for your growth.\nTransform your career with cutting-edge skills and knowledge.',
                  style: GoogleFonts.inter(
                    fontSize: _getResponsiveSize(context, 18),
                    fontWeight: FontWeight.w400,
                    color: Colors.white.withOpacity(0.9),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: _getResponsiveSize(context, 48)),

          // Floating Elements
          Expanded(
            flex: 1,
            child: AnimatedBuilder(
              animation: _rotateAnimation,
              builder: (context, child) {
                return SizedBox(
                  height: _getResponsiveSize(context, 200),
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      // Background circles
                      Positioned(
                        top: _getResponsiveSize(context, 50),
                        right: _getResponsiveSize(context, 50),
                        child: Transform.rotate(
                          angle: _rotateAnimation.value,
                          child: Container(
                            width: _getResponsiveSize(context, 120),
                            height: _getResponsiveSize(context, 120),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.2),
                                width: 2,
                              ),
                            ),
                          ),
                        ),
                      ),

                      Positioned(
                        top: _getResponsiveSize(context, 20),
                        right: _getResponsiveSize(context, 20),
                        child: Transform.rotate(
                          angle: -_rotateAnimation.value * 0.5,
                          child: Container(
                            width: _getResponsiveSize(context, 80),
                            height: _getResponsiveSize(context, 80),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withValues(alpha: 0.1),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlassButton(String text, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: _getResponsiveSize(context, 24),
          vertical: _getResponsiveSize(context, 12),
        ),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(_getResponsiveSize(context, 12)),
          border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: _getResponsiveSize(context, 18),
            ),
            SizedBox(width: _getResponsiveSize(context, 8)),
            Text(
              text,
              style: GoogleFonts.inter(
                fontSize: _getResponsiveSize(context, 14),
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLearningPathsSection(BuildContext context) {
    final List<Map<String, dynamic>> blocks = [
      {
        'title': 'Global Safety',
        'subtitle': 'Essential safety protocols',
        'icon': Icons.health_and_safety,
        'color': const Color(0xFF10B981),
        'route': AppRoute.globalSafety,
      },
      {
        'title': 'Cement Industrial Academy',
        'subtitle': 'Industry expertise',
        'icon': Icons.local_library,
        'color': const Color(0xFF3B82F6),
        'route': AppRoute.cementAcademy,
      },
      {
        'title': 'Holcim University',
        'subtitle': 'Advanced learning',
        'icon': Icons.school,
        'color': const Color(0xFF06B6D4),
        'route': AppRoute.holcimAcademy,
      },
      {
        'title': 'Soft & Leadership Skills',
        'subtitle': 'Personal development',
        'icon': Icons.people,
        'color': const Color(0xFF8B5CF6),
        'route': AppRoute.percipio,
      },
      {
        'title': 'Language Skills',
        'subtitle': 'Global communication',
        'icon': Icons.translate,
        'color': const Color(0xFFEC4899),
        'route': AppRoute.goFluent,
      },
      {
        'title': 'Compliance',
        'subtitle': 'Legal & regulations',
        'icon': Icons.gavel,
        'color': const Color(0xFFEF4444),
        'route': AppRoute.compliance,
      },
    ];

    // Random positions for blocks (adjust these values as needed)
    final List<Map<String, double>> positions = [
      {'left': 0.1, 'top': 0.0, 'width': 370, 'height': 320},
      {'left': 0.55, 'top': 0.05, 'width': 360, 'height': 330},
      {'left': 0.95, 'top': 0.05, 'width': 370, 'height': 330},
      {'left': 0.15, 'top': 0.45, 'width': 350, 'height': 320},
      {'left': 0.5, 'top': 0.5, 'width': 360, 'height': 330},
      {'left': 0.9, 'top': 0.5, 'width': 345, 'height': 320},
    ];

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: _getResponsiveSize(context, 24),
        vertical: _getResponsiveSize(context, 32),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: _getResponsiveSize(context, 24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Learning Paths',
                  style: GoogleFonts.poppins(
                    fontSize: _getResponsiveSize(context, 36),
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: _getResponsiveSize(context, 8)),
                Text(
                  'Choose your journey and start building tomorrow\'s skills today',
                  style: GoogleFonts.inter(
                    fontSize: _getResponsiveSize(context, 18),
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: _getResponsiveSize(context, 40)),

          // Random positioned blocks
          Container(
            height: _getResponsiveSize(
              context,
              800,
            ), // Increased height for bigger blocks
            margin: EdgeInsets.symmetric(
              horizontal: _getResponsiveSize(context, 24),
            ),
            child: Stack(
              children:
                  blocks.asMap().entries.map((entry) {
                    final index = entry.key;
                    final block = entry.value;
                    final position = positions[index];

                    return Positioned(
                      left:
                          MediaQuery.of(context).size.width *
                          position['left']! *
                          0.7,
                      top: _getResponsiveSize(context, 800) * position['top']!,
                      child: SizedBox(
                        width: _getResponsiveSize(context, position['width']!),
                        height: _getResponsiveSize(
                          context,
                          position['height']!,
                        ),
                        child: _buildCompactLearningBlock(
                          title: block['title'],
                          subtitle: block['subtitle'],
                          icon: block['icon'],
                          color: block['color'],
                          index: index,
                          onTap: () => context.go(block['route']),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactLearningBlock({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required int index,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(_getResponsiveSize(context, 28)),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color.withOpacity(0.8), color],
          ),
          borderRadius: BorderRadius.circular(_getResponsiveSize(context, 22)),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.25),
              blurRadius: 18,
              offset: const Offset(0, 8),
            ),
          ],
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon container
            Container(
              padding: EdgeInsets.all(_getResponsiveSize(context, 16)),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(
                  _getResponsiveSize(context, 16),
                ),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: _getResponsiveSize(context, 32),
              ),
            ),

            const Spacer(),

            // Text content
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: _getResponsiveSize(context, 20),
                fontWeight: FontWeight.w600,
                color: Colors.white,
                height: 1.2,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: _getResponsiveSize(context, 8)),
            Text(
              subtitle,
              style: GoogleFonts.inter(
                fontSize: _getResponsiveSize(context, 14),
                color: Colors.white.withOpacity(0.8),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            SizedBox(height: _getResponsiveSize(context, 16)),

            // Arrow indicator
            Row(
              children: [
                Text(
                  'Explore',
                  style: GoogleFonts.inter(
                    fontSize: _getResponsiveSize(context, 12),
                    fontWeight: FontWeight.w500,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_rounded,
                  color: Colors.white,
                  size: _getResponsiveSize(context, 20),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingFooter(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(_getResponsiveSize(context, 18)),
      padding: EdgeInsets.all(_getResponsiveSize(context, 32)),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(_getResponsiveSize(context, 20)),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'Learning ',
                  style: GoogleFonts.poppins(
                    fontSize: _getResponsiveSize(context, 18),
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                TextSpan(
                  text: 'Corner',
                  style: GoogleFonts.poppins(
                    fontSize: _getResponsiveSize(context, 18),
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF06B6D4),
                  ),
                ),
              ],
            ),
          ),
          Text(
            '© 2025 HolCim. All rights reserved.',
            style: GoogleFonts.inter(
              fontSize: _getResponsiveSize(context, 14),
              color: Colors.white.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
}
