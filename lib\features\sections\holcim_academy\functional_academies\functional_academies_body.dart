'assets/functional_academies/three-collegues-working-car-showroom.jpg','Finance'
'assets/functional_academies/creative-monitor-tech-digitally-generated-desk.jpg','Sales & Marketing'
'assets/functional_academies/futuristic-technology-concept.jpg','Procurement'
'assets/functional_academies/aerial-view-cargo-ship-cargo-container-harbor.jpg','Logistics'
'assets/functional_academies/factory-producing-co2-pollution.jpg','Industrial & Decarbonization'
'assets/functional_academies/sphere-with-trees.jpg','Sustainability'

import 'package:cilas_biskra/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;

class CementAcademiesBody extends StatefulWidget {
  const CementAcademiesBody({super.key});

  @override
  State<CementAcademiesBody> createState() => _CementAcademiesBodyState();
}

class _CementAcademiesBodyState extends State<CementAcademiesBody>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _backgroundController;
  late AnimationController _gridController;
  late AnimationController _floatingController;
  
  late Animation<double> _headerSlideAnimation;
  late Animation<double> _headerFadeAnimation;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _gridStaggerAnimation;
  
  bool _isDisposed = false;
  
  final List<Academy> academies = [
    Academy(
      title: 'Project Management',
      subtitle: 'Strategic Leadership & Planning',
      description: 'Master the art of strategic project planning, resource allocation, and team leadership. Learn advanced methodologies including Agile, Scrum, and traditional project management frameworks.',
      details: [
        'Advanced Project Planning & Scheduling',
        'Risk Management & Mitigation Strategies',
        'Team Leadership & Communication',
        'Budget Control & Resource Optimization',
      ],
      icon: Icons.engineering,
      imageUrl: 'assets/images/project_management.jpg', // Add your image path
      gradient: const LinearGradient(
        colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Process & Production',
      subtitle: 'Manufacturing Excellence',
      description: 'Deep dive into cement manufacturing processes, quality control, and production optimization techniques for maximum efficiency and output.',
      details: [
        'Cement Manufacturing Process Optimization',
        'Quality Control & Testing Procedures',
        'Production Line Efficiency',
        'Raw Material Processing & Management',
      ],
      icon: Icons.precision_manufacturing,
      imageUrl: 'assets/images/process_production.jpg',
      gradient: const LinearGradient(
        colors: [Color(0xFFFF6B6B), Color(0xFFEE5A24)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Plant & Leadership',
      subtitle: 'Operational Management',
      description: 'Comprehensive plant operations management, leadership development, and organizational excellence in industrial environments.',
      details: [
        'Plant Operations & Management Systems',
        'Leadership Development & Team Building',
        'Operational Excellence & Lean Manufacturing',
        'Safety Management & Compliance',
      ],
      icon: Icons.factory,
      imageUrl: 'assets/images/plant_leadership.jpg',
      gradient: const LinearGradient(
        colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Quarry',
      subtitle: 'Mining & Extraction',
      description: 'Specialized training in quarry operations, mining techniques, and sustainable extraction practices for raw material procurement.',
      details: [
        'Mining & Extraction Techniques',
        'Quarry Safety & Environmental Compliance',
        'Equipment Operation & Maintenance',
        'Geological Survey & Site Planning',
      ],
      icon: Icons.terrain,
      imageUrl: 'assets/images/quarry.jpg',
      gradient: const LinearGradient(
        colors: [Color(0xFFFFA726), Color(0xFFFF7043)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Maintenance',
      subtitle: 'Technical Operations',
      description: 'Advanced maintenance strategies, preventive care, and technical operations to ensure optimal equipment performance and longevity.',
      details: [
        'Predictive & Preventive Maintenance',
        'Equipment Diagnostics & Troubleshooting',
        'Maintenance Planning & Scheduling',
        'Technical Documentation & Reporting',
      ],
      icon: Icons.build_circle,
      imageUrl: 'assets/images/maintenance.jpg',
      gradient: const LinearGradient(
        colors: [Color(0xFF9C27B0), Color(0xFF673AB7)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    Academy(
      title: 'Quality & Environment',
      subtitle: 'Standards & Sustainability',
      description: 'Environmental sustainability, quality assurance, and compliance with international standards for responsible manufacturing.',
      details: [
        'Environmental Impact Assessment',
        'ISO Standards & Quality Management',
        'Sustainability & Green Manufacturing',
        'Regulatory Compliance & Auditing',
      ],
      icon: Icons.eco,
      imageUrl: 'assets/images/quality_environment.jpg',
      gradient: const LinearGradient(
        colors: [Color(0xFF66BB6A), Color(0xFF43A047)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _gridController = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );
    
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _headerSlideAnimation = Tween<double>(begin: -100, end: 0).animate(
      CurvedAnimation(parent: _headerController, curve: Curves.elasticOut),
    );
    
    _headerFadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _headerController, curve: Curves.easeInOut),
    );
    
    _backgroundAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _backgroundController, curve: Curves.easeInOut),
    );
    
    _gridStaggerAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _gridController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimationSequence() async {
    if (_isDisposed) return;
    
    _floatingController.repeat(reverse: true);
    _backgroundController.forward();
    
    await Future.delayed(const Duration(milliseconds: 300));
    if (_isDisposed) return;
    
    _headerController.forward();
    
    await Future.delayed(const Duration(milliseconds: 500));
    if (_isDisposed) return;
    
    _gridController.forward();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _headerController.dispose();
    _backgroundController.dispose();
    _gridController.dispose();
    _floatingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          _buildAnimatedBackground(),
          CustomScrollView(
            slivers: [
              _buildAnimatedHeader(),
              SliverToBoxAdapter(
                child: _buildResponsiveGrid(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF0F0F23).withOpacity(0.9),
                const Color(0xFF1A1A2E).withOpacity(0.8),
                const Color(0xFF16213E).withOpacity(0.7),
              ],
              stops: [
                0.0,
                _backgroundAnimation.value * 0.5,
                _backgroundAnimation.value,
              ],
            ),
          ),
          child: AnimatedBuilder(
            animation: _floatingController,
            builder: (context, child) {
              return CustomPaint(
                painter: FloatingParticlesPainter(_floatingController.value),
                size: Size.infinite,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildAnimatedHeader() {
    return SliverAppBar(
      expandedHeight: 300,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        background: AnimatedBuilder(
          animation: _headerController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, _headerSlideAnimation.value),
              child: Opacity(
                opacity: _headerFadeAnimation.value,
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Color(0xFF0F0F23),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Holcim Functional',
                          style: TextStyle(
                            fontSize: 48,
                            fontWeight: FontWeight.w300,
                            color: Colors.white70,
                            letterSpacing: 2,
                          ),
                        ),
                        Text(
                          'ACADEMIES',
                          style: TextStyle(
                            fontSize: 64,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            letterSpacing: 8,
                            height: 0.8,
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Excellence in Cement Manufacturing',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            color: Colors.white60,
                            letterSpacing: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildResponsiveGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        
        int crossAxisCount;
        double childAspectRatio;
        double spacing;
        
        if (screenWidth > 2400) {
          crossAxisCount = 3;
          childAspectRatio = 0.8;
          spacing = 32;
        } else if (screenWidth > 1920) {
          crossAxisCount = 3;
          childAspectRatio = 0.75;
          spacing = 24;
        } else if (screenWidth > 1200) {
          crossAxisCount = 2;
          childAspectRatio = 0.7;
          spacing = 20;
        } else {
          crossAxisCount = 1;
          childAspectRatio = 0.9;
          spacing = 16;
        }
        
        return AnimatedBuilder(
          animation: _gridStaggerAnimation,
          builder: (context, child) {
            return Padding(
              padding: EdgeInsets.all(screenWidth > 1920 ? 64 : 32),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: childAspectRatio,
                  crossAxisSpacing: spacing,
                  mainAxisSpacing: spacing,
                ),
                itemCount: academies.length,
                itemBuilder: (context, index) {
                  final delay = index * 0.1;
                  final animationValue = (_gridStaggerAnimation.value - delay).clamp(0.0, 1.0);
                  
                  return Transform.scale(
                    scale: Curves.elasticOut.transform(animationValue),
                    child: Opacity(
                      opacity: animationValue,
                      child: ModernAcademyCard(
                        academy: academies[index],
                        onTap: () => _handleAcademyTap(context, academies[index].title),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  void _handleAcademyTap(BuildContext context, String title) {
    switch (title) {
      case 'Process & Production':
        context.go('${AppRoute.cementDetails}/process-production');
        break;
      case 'Quarry':
        context.go('${AppRoute.cementDetails}/quarry');
        break;
      case 'Quality & Environment':
        context.go('${AppRoute.cementDetails}/quality-environment');
        break;
      case 'Maintenance':
        context.go('${AppRoute.cementDetails}/maintenance');
        break;
      case 'Project Management':
        context.go('${AppRoute.cementDetails}/project-management');
        break;
      case 'Plant & Leadership':
        context.go('${AppRoute.cementDetails}/plant-leadership');
        break;
      default:
        context.go(AppRoute.cementDetails);
        break;
    }
  }
}

class Academy {
  final String title;
  final String subtitle;
  final String description;
  final List<String> details;
  final IconData icon;
  final String imageUrl;
  final LinearGradient gradient;

  Academy({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.details,
    required this.icon,
    required this.imageUrl,
    required this.gradient,
  });
}

class ModernAcademyCard extends StatefulWidget {
  final Academy academy;
  final VoidCallback onTap;

  const ModernAcademyCard({
    super.key,
    required this.academy,
    required this.onTap,
  });

  @override
  State<ModernAcademyCard> createState() => _ModernAcademyCardState();
}

class _ModernAcademyCardState extends State<ModernAcademyCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _dropdownController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _dropdownAnimation;

  bool _isHovered = false;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _dropdownController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeOut),
    );

    _elevationAnimation = Tween<double>(begin: 8.0, end: 20.0).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.01).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeOut),
    );

    _dropdownAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _dropdownController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _dropdownController.dispose();
    super.dispose();
  }

  void _toggleDropdown() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _dropdownController.forward();
    } else {
      _dropdownController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _dropdownController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: _elevationAnimation.value,
                    offset: Offset(0, _elevationAnimation.value / 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Main Card
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _toggleDropdown,
                        onHover: (hovered) {
                          setState(() => _isHovered = hovered);
                          if (hovered) {
                            _hoverController.forward();
                          } else {
                            _hoverController.reverse();
                          }
                        },
                        borderRadius: BorderRadius.circular(24),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: widget.academy.gradient,
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Stack(
                            children: [
                              // Background Image
                              Positioned.fill(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(24),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      image: DecorationImage(
                                        image: AssetImage(widget.academy.imageUrl),
                                        fit: BoxFit.cover,
                                        colorFilter: ColorFilter.mode(
                                          Colors.black.withOpacity(0.4),
                                          BlendMode.darken,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              // Gradient Overlay
                              Positioned.fill(
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Colors.transparent,
                                        widget.academy.gradient.colors.first.withOpacity(0.8),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(24),
                                  ),
                                ),
                              ),
                              // Background pattern
                              Positioned.fill(
                                child: CustomPaint(
                                  painter: CardPatternPainter(_hoverController.value),
                                ),
                              ),
                              // Content
                              Padding(
                                padding: const EdgeInsets.all(24),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Icon(
                                          widget.academy.icon,
                                          size: 40,
                                          color: Colors.white.withOpacity(0.9),
                                        ),
                                        AnimatedRotation(
                                          duration: const Duration(milliseconds: 300),
                                          turns: _isExpanded ? 0.5 : 0,
                                          child: Icon(
                                            Icons.keyboard_arrow_down,
                                            color: Colors.white.withOpacity(0.8),
                                            size: 24,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    Text(
                                      widget.academy.title,
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.white,
                                        height: 1.2,
                                      ),
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      widget.academy.subtitle,
                                      style: TextStyle(
                                        fontSize: 13,
                                        fontWeight: FontWeight.w400,
                                        color: Colors.white.withOpacity(0.8),
                                        height: 1.4,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    AnimatedContainer(
                                      duration: const Duration(milliseconds: 300),
                                      width: _isHovered ? 50 : 35,
                                      height: 3,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.8),
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // Dropdown Details
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 400),
                    height: _dropdownAnimation.value * 280,
                    curve: Curves.easeInOut,
                    child: Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(top: 8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.all(20),
                          child: Opacity(
                            opacity: _dropdownAnimation.value,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'About This Academy',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color: widget.academy.gradient.colors.first,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Text(
                                  widget.academy.description,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.black87,
                                    height: 1.5,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Key Learning Areas',
                                  style: TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w600,
                                    color: widget.academy.gradient.colors.first,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                ...widget.academy.details.map((detail) => Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 4),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 6,
                                        height: 6,
                                        margin: const EdgeInsets.only(top: 6, right: 12),
                                        decoration: BoxDecoration(
                                          color: widget.academy.gradient.colors.first,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          detail,
                                          style: const TextStyle(
                                            fontSize: 13,
                                            color: Colors.black87,
                                            height: 1.4,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                                const SizedBox(height: 16),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    onPressed: widget.onTap,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: widget.academy.gradient.colors.first,
                                      foregroundColor: Colors.white,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    child: const Text(
                                      'Explore Academy',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class FloatingParticlesPainter extends CustomPainter {
  final double animationValue;

  FloatingParticlesPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 20; i++) {
      final x = (size.width / 20) * i + (math.sin(animationValue * 2 * math.pi + i) * 50);
      final y = (size.height / 4) + (math.cos(animationValue * 2 * math.pi + i * 0.5) * 100);
      final radius = 2 + (math.sin(animationValue * 2 * math.pi + i * 2) * 2);
      
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class CardPatternPainter extends CustomPainter {
  final double animationValue;

  CardPatternPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1 * animationValue)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    final path = Path();
    path.moveTo(size.width * 0.8, 0);
    path.quadraticBezierTo(
      size.width, size.height * 0.2,
      size.width, size.height * 0.5,
    );
    path.quadraticBezierTo(
      size.width * 0.8, size.height * 0.8,
      size.width * 0.6, size.height,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}