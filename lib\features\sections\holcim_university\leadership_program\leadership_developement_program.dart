import 'package:cilas_biskra/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class LeadershipDevelopmentScreen extends StatefulWidget {
  const LeadershipDevelopmentScreen({super.key});

  @override
  State<LeadershipDevelopmentScreen> createState() =>
      _LeadershipDevelopmentScreenState();
}

class _LeadershipDevelopmentScreenState
    extends State<LeadershipDevelopmentScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _cardsController;
  late Animation<double> _headerAnimation;
  late Animation<double> _cardsAnimation;

  @override
  void initState() {
    super.initState();
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _cardsController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _headerAnimation = CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic,
    );
    _cardsAnimation = CurvedAnimation(
      parent: _cardsController,
      curve: Curves.easeOutBack,
    );

    // Start animations
    _headerController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _cardsController.forward();
    });
  }

  @override
  void dispose() {
    _headerController.dispose();
    _cardsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 1400;

    return GestureDetector(
      onHorizontalDragEnd: (details) {
        if (details.primaryVelocity! > 0) {
          context.go(AppRoute.holcimAcademy);
        }
      },
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFF8FAFC),
                const Color(0xFFE2E8F0),
                const Color(0xFFF1F5F9),
              ],
            ),
          ),
          child: SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: isLargeScreen ? 64.0 : 32.0,
                      vertical: 40.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildAnimatedHeader(isLargeScreen),
                        SizedBox(height: isLargeScreen ? 60 : 40),
                        _buildMainContent(isLargeScreen),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedHeader(bool isLargeScreen) {
    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _headerAnimation.value)),
          child: Opacity(
            opacity: _headerAnimation.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ShaderMask(
                  shaderCallback:
                      (bounds) => LinearGradient(
                        colors: [
                          const Color(0xFF0F172A),
                          const Color(0xFF1E40AF),
                          const Color(0xFF0EA5E9),
                          const Color(0xFF10B981),
                        ],
                      ).createShader(bounds),
                  child: Text(
                    'Leadership Development',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 48 : 32,
                      fontWeight: FontWeight.w900,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(isLargeScreen ? 32 : 24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF1E40AF),
                        const Color(0xFF3B82F6),
                        const Color(0xFF0EA5E9),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF1E40AF).withOpacity(0.25),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'TALENT IS NURTURED',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: isLargeScreen ? 14 : 12,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 1.5,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'HOLCIM UNIVERSITY\nWHERE GROWTH STARTS WITH US',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: isLargeScreen ? 28 : 20,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMainContent(bool isLargeScreen) {
    return AnimatedBuilder(
      animation: _cardsAnimation,
      builder: (context, child) {
        return isLargeScreen
            ? _buildLargeScreenLayout()
            : _buildCompactLayout();
      },
    );
  }

  Widget _buildLargeScreenLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: 7, child: _buildLeadershipLevels(true)),
        const SizedBox(width: 40),
        Expanded(flex: 3, child: _buildSideCards(true)),
      ],
    );
  }

  Widget _buildCompactLayout() {
    return Column(
      children: [
        _buildLeadershipLevels(false),
        const SizedBox(height: 32),
        _buildSideCards(false),
      ],
    );
  }

  Widget _buildLeadershipLevels(bool isLargeScreen) {
    final levels = [
      {
        'title': 'Senior Leader',
        'subtitle': 'Business School for Senior Leaders',
        'gradient': [const Color(0xFF1E40AF), const Color(0xFF3B82F6)],
        'delay': 0,
      },
      {
        'title': 'Senior Manager',
        'subtitle': 'Business School for Advanced Leaders I & II',
        'gradient': [const Color(0xFF7C3AED), const Color(0xFF8B5CF6)],
        'delay': 100,
      },
      {
        'title': 'Middle Manager',
        'subtitle': 'Functional Academies',
        'gradient': [const Color(0xFF059669), const Color(0xFF10B981)],
        'delay': 200,
      },
      {
        'title': 'Team Leader',
        'subtitle': 'Business School for Emerging Leaders',
        'gradient': [const Color(0xFFDC2626), const Color(0xFFEF4444)],
        'delay': 300,
      },
      {
        'title': 'Early Career',
        'subtitle': 'Early Career Leadership Program',
        'gradient': [const Color(0xFFEA580C), const Color(0xFFF97316)],
        'delay': 400,
      },
    ];

    return Column(
      children:
          levels.asMap().entries.map((entry) {
            final level = entry.value;

            return TweenAnimationBuilder<double>(
              duration: Duration(milliseconds: 800 + (level['delay'] as int)),
              tween: Tween(begin: 0.0, end: 1.0),
              curve: Curves.elasticOut,
              builder: (context, value, child) {
                return Transform.scale(
                  scale: 0.8 + (0.2 * value),
                  child: Transform.translate(
                    offset: Offset(100 * (1 - value), 0),
                    child: Opacity(
                      opacity: value.clamp(0.0, 1.0),
                      child: Container(
                        margin: EdgeInsets.only(
                          bottom: isLargeScreen ? 20 : 16,
                        ),
                        child: _buildLevelCard(
                          level['title'] as String,
                          level['subtitle'] as String,
                          level['gradient'] as List<Color>,
                          isLargeScreen,
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
    );
  }

  Widget _buildLevelCard(
    String title,
    String subtitle,
    List<Color> gradientColors,
    bool isLargeScreen,
  ) {
    return MouseRegion(
      onEnter: (_) {},
      child: GestureDetector(
        onTap: () {
          context.go(AppRoute.holcimLeadershipCards, extra: subtitle);
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          width: double.infinity,
          padding: EdgeInsets.all(isLargeScreen ? 28 : 20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradientColors,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: gradientColors[0].withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: isLargeScreen ? 20 : 16,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.3,
                ),
              ),
              if (subtitle.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: isLargeScreen ? 16 : 14,
                    fontWeight: FontWeight.w500,
                    height: 1.3,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSideCards(bool isLargeScreen) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutBack,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(50 * (1 - value), 0),
          child: Opacity(
            opacity: value.clamp(0.0, 1.0),
            child: Column(
              children: [
                _buildInfoCard(
                  'Holcim Forums',
                  'Interactive Learning Sessions',
                  Icons.forum_outlined,
                  const Color(0xFF8B5CF6),
                  isLargeScreen,
                ),
                SizedBox(height: isLargeScreen ? 24 : 16),
                _buildInfoCard(
                  'Online Learning',
                  'Digital Education Platform',
                  Icons.laptop_mac_outlined,
                  const Color(0xFF10B981),
                  isLargeScreen,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoCard(
    String title,
    String subtitle,
    IconData icon,
    Color accentColor,
    bool isLargeScreen,
  ) {
    return GestureDetector(
      onTap: () {
        if (title == 'Holcim Forums') {
          context.go(AppRoute.forumCard);
        }
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(isLargeScreen ? 24 : 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: accentColor,
                size: isLargeScreen ? 28 : 24,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                color: const Color(0xFF1F2937),
                fontSize: isLargeScreen ? 18 : 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: const Color(0xFF6B7280),
                fontSize: isLargeScreen ? 14 : 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
