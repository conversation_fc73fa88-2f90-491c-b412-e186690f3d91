import 'package:flutter/material.dart';
import 'dart:ui';

class PercipioShowcase extends StatefulWidget {
  const PercipioShowcase({super.key});

  @override
  State<PercipioShowcase> createState() => _PercipioShowcaseState();
}

class _PercipioShowcaseState extends State<PercipioShowcase>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _pageAnimation;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _pageAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(color: const Color(0xFF0A2463)),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                height: 80,
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 150,
                        child: Center(
                          child: Image.asset(
                            'assets/icons/Percipio-Logo-v2.png',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  children: [
                    WhatIsPercipioPage(animation: _pageAnimation),
                    //TipsPage(animation: _pageAnimation),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PageIndicator extends StatelessWidget {
  final int currentPage;
  final int pageCount;
  final Function(int) onPageSelected;

  const PageIndicator({
    super.key,
    required this.currentPage,
    required this.pageCount,
    required this.onPageSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(
        pageCount,
        (index) => GestureDetector(
          onTap: () => onPageSelected(index),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.symmetric(horizontal: 8),
            height: 10,
            width: currentPage == index ? 32 : 10,
            decoration: BoxDecoration(
              color: const Color(0xFF0A2463),
              borderRadius: BorderRadius.circular(5),
            ),
          ),
        ),
      ),
    );
  }
}

class WhatIsPercipioPage extends StatelessWidget {
  final Animation<double> animation;

  const WhatIsPercipioPage({super.key, required this.animation});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with animation
          FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, -0.2),
                end: Offset.zero,
              ).animate(animation),
              child: const Text(
                "🧠 What is Percipio?",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 40,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 30),

          // Content with animation
          FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.2),
                end: Offset.zero,
              ).animate(animation),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withOpacity(0.15),
                          Colors.white.withOpacity(0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(color: Colors.white.withOpacity(0.2)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "📚 Percipio – Your Gateway to Smarter Learning",
                          style: TextStyle(
                            color: Color(0xFF8CD44A),
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          "Percipio is Holcim's all-in-one online learning platform for personal and professional development. With a single sign-on, you unlock access to:",
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                        const SizedBox(height: 20),
                        // Features list
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 1,
                              child: _buildFeatureItem(
                                "🎓",
                                "Hundreds of Courses",
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: _buildFeatureItem(
                                "📹",
                                "Videos, 🎧 Audiobooks, 📄 Articles, 📘 E-books",
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 1,
                              child: _buildFeatureItem(
                                "🧠",
                                "Interactive Resources & Summaries",
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: _buildFeatureItem(
                                "🌐",
                                "Support for 12 languages via goFLUENT for AI-powered language learning",
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 40),

                        // Learning Areas
                        const Text(
                          "🎯 Learning Areas",
                          style: TextStyle(
                            color: Color(0xFF8CD44A),
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          "You can watch, read, listen, and practice content anytime, anywhere in key focus areas:",
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                        const SizedBox(height: 20),
                        // Learning areas grid
                        Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          children: [
                            _buildLearningArea("Leadership & Management"),
                            _buildLearningArea("Business & Tech Skills"),
                            _buildLearningArea("Digital Transformation"),
                            _buildLearningArea("Project Management"),
                            _buildLearningArea("Productivity & Collaboration"),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          "🎓 Other key topics include: Coaching, Change Management, Crisis Management, Negotiation Skills, Handling Pay Raises, Leading with Empathy, and much more.",
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                        const SizedBox(height: 40),

                        // Smart Learning Tools
                        const Text(
                          "🚀 Smart Learning Tools",
                          style: TextStyle(
                            color: Color(0xFF8CD44A),
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 20),
                        Row(
                          children: [
                            Expanded(
                              child: _buildSmartTool(
                                "✅",
                                "goFLUENT",
                                "AI-powered language coaching",
                              ),
                            ),
                            Expanded(
                              child: _buildSmartTool(
                                "🧠",
                                "CAISY",
                                "Smart conversation trainer for real-life simulations",
                              ),
                            ),
                            Expanded(
                              child: _buildSmartTool(
                                "📱",
                                "Mobile App",
                                "Offline Mode included",
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 40),

                        // How to Access
                        const Text(
                          "📥 How to Access?",
                          style: TextStyle(
                            color: Color(0xFF8CD44A),
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _buildAccessStep(
                              "1",
                              "Use your app launcher via Gmail",
                            ),
                            _buildAccessStep("2", "Visit: holcim.percipio.com"),
                            SizedBox(width: 20),
                            SizedBox(
                              width: 100,
                              child: Image.asset(
                                'assets/images/percipio_qrcode.png',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLearningArea(String title) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF0A2463).withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF8CD44A).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSmartTool(String emoji, String title, String description) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Text(emoji, style: const TextStyle(fontSize: 32)),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          title == 'Mobile App'
              ? Expanded(
                flex: 1,
                child: Image.asset('assets/images/onelinkto_38fg55.png'),
              )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildAccessStep(String number, String text) {
    return Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: const Color(0xFF8CD44A),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Text(text, style: const TextStyle(color: Colors.white, fontSize: 16)),
      ],
    );
  }
}
