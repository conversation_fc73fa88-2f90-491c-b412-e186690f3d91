import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

// Wrapper class for router integration
class PlantLeadershipApp extends StatelessWidget {
  final String? initialPage;

  const PlantLeadershipApp({super.key, this.initialPage});

  @override
  Widget build(BuildContext context) {
    return MainPage(initialPage: initialPage);
  }
}

class MainPage extends StatefulWidget {
  final String? initialPage;

  const MainPage({super.key, this.initialPage});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  final List<Widget> pages = const [
    PlantLeadershipPage(),
    ProjectManagementPage(),
  ];

  final List<String> pageTitles = [
    'Plant Leadership Programs',
    'Project Management Programs',
  ];

  @override
  void initState() {
    super.initState();

    // Set initial page based on parameter
    if (widget.initialPage != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setInitialPage(widget.initialPage!);
      });
    }
  }

  void _setInitialPage(String pageName) {
    final pageMap = {'plant-leadership': 0, 'project-management': 1};

    final pageIndex = pageMap[pageName.toLowerCase()];
    if (pageIndex != null && pageIndex < pages.length) {
      setState(() {
        _currentPage = pageIndex;
      });
      _controller.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          pageTitles[_currentPage],
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF006B82),
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Page content
          Expanded(
            child: PageView.builder(
              physics: const NeverScrollableScrollPhysics(),
              controller: _controller,
              itemCount: pages.length,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemBuilder:
                  (context, index) => Animate(
                    effects: [
                      const FadeEffect(duration: Duration(milliseconds: 600)),
                      const ScaleEffect(
                        begin: Offset(0.9, 0.9),
                        end: Offset(1.0, 1.0),
                        duration: Duration(milliseconds: 600),
                      ),
                    ],
                    child: pages[index],
                  ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

class PlantLeadershipPage extends StatelessWidget {
  const PlantLeadershipPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ProgramCard(
              title: 'PLANT MANAGER SEMINAR',
              subtitle: 'Turning vision into reality',
              content: [
                '''Crafting the future with a strong team and robust
                  processes. The 1-week seminar enables plant
                  managers to deliver on the company's strategic
                  objectives by strengthening their leadership in
                  manufacturing performance.
                  It's about:
                  \n''',
                '• Being aware of how decisions and choices impact on plant performance and country financial results.',
                '• Understanding the levers to drive and execute our decarbonisation journey.',
                '• Extending learning through sharing of experiences.',
                'Target: Plant Managers (nominated).',
                'Contact: Alessandra Noris (Head of L&D, CEM).',
              ],
              color: const Color(0xFF006B82),
              icon: Icons.account_balance,
            ),
          ),
          const SizedBox(width: 32),
          Expanded(
            child: ProgramCard(
              title: 'AMEA FUTURE PLANT MANAGERS',
              subtitle: 'Development path for potential PMs',
              content: [
                '• The program is for potential plant managers to fast track their readiness ideally from < 3 years to ready now.',
                '• The main objective is to bridge their competency gaps on Leadership, Business and Technical skills ensuring a consistent and strong Holcim company culture.',
                '•They will also be prepared to embrace and implement future industrial technologies.',
                'Target: N-1 level PMs (nominated)',
                'Contact: Arshad Tarar (L&D Program Manager)',
              ],
              color: const Color(0xFFE3B505),
              icon: Icons.trending_up,
            ),
          ),
        ],
      ),
    );
  }
}

class ProjectManagementPage extends StatelessWidget {
  const ProjectManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF8CC152), Color(0xFFE3B505)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.engineering,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Project Management Excellence',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Building world-class project management capabilities',
                        style: TextStyle(fontSize: 16, color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          // Cards section
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: ProgramCard(
                    title: 'Project Management Fundamentals',
                    subtitle: 'Global Program',
                    content: [
                      '• PM fundamentals + Holcim success criteria',
                      '• CAPM exam prep',
                      '• For PMs with 2-3 yrs exp',
                      '→ 6 months, 14 days commitment',
                    ],
                    color: const Color(0xFF8CC152),
                    icon: Icons.school,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ProgramCard(
                    title: 'Advanced CAPEX Project Management',
                    subtitle: 'By Region',
                    content: [
                      '• Team + stakeholder engagement, risk & quality',
                      '• PMP exam prep',
                      '• For PMs with >7 yrs exp',
                      '→ 6 months, 17 days commitment',
                    ],
                    color: const Color(0xFFE3B505),
                    icon: Icons.construction,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ProgramCard(
                    title: 'Strategic CAPEX Project Management',
                    subtitle: 'Global Program',
                    content: [
                      '• Strategy implementation, governance, performance',
                      '• PMP cert required + 5 yrs exp',
                      '→ 4 months, 9 days (+5 for PMP cert)',
                    ],
                    color: const Color(0xFF006B82),
                    icon: Icons.analytics,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Shared widget for program cards
class ProgramCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<String> content;
  final Color color;
  final IconData icon;

  const ProgramCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.content,
    this.color = const Color(0xFF006B82),
    this.icon = Icons.business,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, color: color, size: 28),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              ...content.map(
                (c) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 3),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (c.startsWith('•'))
                        Container(
                          margin: const EdgeInsets.only(top: 6, right: 8),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                          ),
                        ),
                      Expanded(
                        child: Text(
                          c.startsWith('•') ? c.substring(2) : c,
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                c.startsWith('Target:') ||
                                        c.startsWith('Contact:')
                                    ? color
                                    : Colors.grey.shade700,
                            fontWeight:
                                c.startsWith('Target:') ||
                                        c.startsWith('Contact:')
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
