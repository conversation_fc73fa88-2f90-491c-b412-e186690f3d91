import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:responsive_framework/responsive_framework.dart';

void main() => runApp(const MyApp());

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      builder: (context, child) => ResponsiveWrapper.builder(
        BouncingScrollWrapper.builder(context, child!),
        maxWidth: 1920,
        minWidth: 480,
        defaultScale: true,
        breakpoints: [
          const ResponsiveBreakpoint.resize(480, name: MOBILE),
          const ResponsiveBreakpoint.autoScale(800, name: TABLET),
          const ResponsiveBreakpoint.autoScale(1000, name: DESKTOP),
          const ResponsiveBreakpoint.autoScale(2460, name: "4K"),
        ],
      ),
      home: const MainPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final PageController _controller = PageController();
  final List<Widget> pages = const [
    PlantLeadershipPage(),
    ProjectManagementPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      body: PageView.builder(
        controller: _controller,
        itemCount: pages.length,
        itemBuilder: (context, index) => Animate(
          effects: const [
            FadeEffect(duration: Duration(milliseconds: 800)),
            ScaleEffect(begin: 0.9, end: 1.0, duration: Duration(milliseconds: 800)),
          ],
          child: pages[index],
        ),
      ),
    );
  }
}

class PlantLeadershipPage extends StatelessWidget {
  const PlantLeadershipPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildCard(
              title: 'PLANT MANAGER SEMINAR',
              subtitle: 'Turning vision into reality',
              content: [
                '• How decisions impact plant performance and finances',
                '• Understanding levers for decarbonisation',
                '• Learning via shared experiences',
                'Target: Plant Managers (nominated)',
                'Contact: Alessandra Noris (Head of L&D, CEM)',
              ],
            ),
          ),
          const SizedBox(width: 32),
          Expanded(
            child: _buildCard(
              title: 'AMEA FUTURE PLANT MANAGERS',
              subtitle: 'Development path for potential PMs',
              content: [
                '• Bridge leadership, business, and technical gaps',
                '• Learn culture, tech, and strategy through hands-on and shadowing',
                '• Exposure to other plants + Six Sigma tools',
                'Target: N-1 level PMs (nominated)',
                'Contact: Arshad Tarar (L&D Program Manager)',
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildCard({required String title, required String subtitle, required List<String> content}) {
    return Card(
      elevation: 10,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text(subtitle, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.grey)),
            const SizedBox(height: 16),
            ...content.map((c) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Text(c, style: const TextStyle(fontSize: 14)),
                )),
          ],
        ),
      ),
    );
  }
}

class ProjectManagementPage extends StatelessWidget {
  const ProjectManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildCard(
              title: 'Project Management Fundamentals',
              subtitle: 'Global',
              content: [
                '• PM fundamentals + Holcim success criteria',
                '• CAPM exam prep',
                '• For PMs with 2-3 yrs exp',
                '→ 6 months, 14 days commitment',
              ],
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildCard(
              title: 'Advanced CAPEX Project Management',
              subtitle: 'By Region',
              content: [
                '• Team + stakeholder engagement, risk & quality',
                '• PMP exam prep',
                '• For PMs with >7 yrs exp',
                '→ 6 months, 17 days commitment',
              ],
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildCard(
              title: 'Strategic CAPEX Project Management',
              subtitle: 'Global',
              content: [
                '• Strategy implementation, governance, performance',
                '• PMP cert required + 5 yrs exp',
                '→ 4 months, 9 days (+5 for PMP cert)',
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({required String title, required String subtitle, required List<String> content}) {
    return Card(
      elevation: 10,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text(subtitle, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.grey)),
            const SizedBox(height: 16),
            ...content.map((c) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Text(c, style: const TextStyle(fontSize: 14)),
                )),
          ],
        ),
      ),
    );
  }
}
