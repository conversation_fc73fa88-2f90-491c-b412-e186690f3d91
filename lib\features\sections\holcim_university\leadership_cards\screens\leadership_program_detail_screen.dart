import 'package:cilas_biskra/features/sections/holcim_university/functional_academies/functional_academies.dart';
import 'package:flutter/material.dart';

class LeadershipProgramDetailScreen extends StatelessWidget {
  final String programTitle;

  const LeadershipProgramDetailScreen({super.key, required this.programTitle});

  @override
  Widget build(BuildContext context) {
    // Get program details based on program title (comparing with subtitle)
    final program = _getProgramBySubtitle(programTitle);

    if (program == null) {
      return FunctionalAcademies();
    }
    // Return just the card content without Scaffold
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: program.color.withValues(alpha: 0.2),
            blurRadius: 30,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(32),
        child: Stack(
          children: [
            // Program content
            SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(30.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: program.color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Icon(
                            program.icon,
                            color: program.color,
                            size: 48,
                          ),
                        ),
                        const SizedBox(width: 26),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                program.title,
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF0F3A4D),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: program.color.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  program.format,
                                  style: TextStyle(
                                    color: program.color,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Focus Areas
                    _buildSection(
                      'Focus Areas',
                      program.focusAreas,
                      program.color,
                    ),
                    const SizedBox(height: 12),

                    // Program Highlights
                    _buildSection(
                      'Program Highlights',
                      program.highlights,
                      program.color,
                    ),
                    const SizedBox(height: 12),

                    // Target Audience
                    _buildSection(
                      'Target Audience',
                      program.audience,
                      program.color,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF0F3A4D),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withValues(alpha: 0.2)),
          ),
          child: Text(
            content,
            style: TextStyle(
              fontSize: 16,
              height: 1.5,
              color: Colors.grey.shade800,
            ),
          ),
        ),
      ],
    );
  }

  ProgramItem? _getProgramBySubtitle(String subtitle) {
    final List<ProgramItem> programs = [
      ProgramItem(
        id: 1,
        title: "Business School For Senior Leaders",
        focusAreas:
            ''' - Leading for effective execution, driving performance and accountability, and leading change – in the new context of building a net zero future
- Develop new leadership skills that are critical for this future
- Support a positive and performance-oriented culture that builds the organization
- Expand interpersonal networks and have fun!''',
        highlights:
            "Deep dive into Engaging People, with special focus on topics such as Emotional Intelligence, Cognitive Diversity, Resilience and Change Management",
        audience: "All members of the Senior Leaders Group",
        format: "Virtual",
        color: Colors.green.shade700,
        icon: Icons.people,
      ),
      ProgramItem(
        id: 2,
        title: "Business School for Advanced Leaders I & II",
        focusAreas:
            "Executing in a changing environment and engaging employees",
        highlights:
            "Interactive virtual sessions led by Ivey School of Business faculty, covering topics like green growth, organizational change, diversity, and leadership narratives.",
        audience:
            "Advanced leaders responsible for significant organizational areas",
        format: "Virtual",
        color: Colors.blue.shade800,
        icon: Icons.school,
      ),
      ProgramItem(
        id: 3,
        title: "Business School for Emerging Leaders",
        focusAreas: "Leadership, strategy, and business acumen",
        highlights:
            "Includes FranklinCovey's \"The 4 Essential Roles of Leadership,\" Harvard simulations, strategic tools, financial understanding, and 1:1 coaching.",
        audience: "Mid-level leaders nominated for the program",
        format: "4 months modular program",
        color: Colors.blue.shade600,
        icon: Icons.trending_up,
      ),
      ProgramItem(
        id: 4,
        title: "Early Career Leadership Program",
        focusAreas:
            "Leadership development, business skills, and exposure to senior management",
        highlights:
            "Virtual program featuring FranklinCovey leadership training, business simulations, keynote webinars, and coaching.",
        audience: "Early career leaders nominated for the program",
        format: "6 months",
        color: Colors.blue.shade400,
        icon: Icons.rocket_launch,
      ),
    ];
    // Compare by subtitle (case-insensitive)
    final matchingPrograms = programs.where(
      (program) => program.title.toLowerCase() == subtitle.toLowerCase(),
    );
    return matchingPrograms.isNotEmpty ? matchingPrograms.first : null;
  }
}

class ProgramItem {
  final int id;
  final String title;
  final String focusAreas;
  final String highlights;
  final String audience;
  final String format;
  final Color color;
  final IconData icon;

  ProgramItem({
    required this.id,
    required this.title,
    required this.focusAreas,
    required this.highlights,
    required this.audience,
    required this.format,
    required this.color,
    required this.icon,
  });
}
