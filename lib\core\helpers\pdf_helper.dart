import 'dart:io';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

/// Helper class for PDF-related functionality
class PDFHelper {
  /// Builds a PDF viewer widget from a File
  ///
  /// This method creates a fullscreen PDF viewer with the following features:
  /// - Displays in fullscreen mode
  /// - Automatically opens in fit width mode
  /// - Removes the app bar to maximize viewing area
  static Widget buildPDFView(File pdfFile) {
    return SfPdfViewer.file(
      pdfFile,
      enableDoubleTapZooming: true,
      canShowScrollHead: true,
      canShowScrollStatus: true,
      pageSpacing: 0,
      initialZoomLevel: 1.0,
      interactionMode: PdfInteractionMode.pan,
      initialScrollOffset: Offset.zero,
      canShowPaginationDialog: true,
      // Start with fit width mode for better reading experience
      pageLayoutMode: PdfPageLayoutMode.single,
    );
  }

  /// Opens a PDF in fullscreen mode
  ///
  /// This method creates a new screen that displays the PDF in fullscreen
  /// without any UI elements like app bar or navigation
  static void openPDFInFullscreen(BuildContext context, File pdfFile) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => Scaffold(
              // No app bar for maximum viewing area
              body: SafeArea(
                // Use GestureDetector to allow closing with a tap
                child: Stack(
                  children: [
                    // PDF viewer taking the full screen
                    SfPdfViewer.file(
                      pdfFile,
                      enableDoubleTapZooming: true,
                      canShowScrollHead: true,
                      canShowScrollStatus: true,
                      pageSpacing: 0,
                      initialZoomLevel: 1.0,
                      interactionMode: PdfInteractionMode.pan,
                      initialScrollOffset: Offset.zero,
                      canShowPaginationDialog: true,
                      // Start with fit width mode for better reading experience
                      pageLayoutMode: PdfPageLayoutMode.single,
                    ),
                    // Close button positioned at the top-right
                    Positioned(
                      top: 10,
                      right: 10,
                      child: IconButton(
                        icon: const Icon(
                          Icons.close,
                          color: Colors.black54,
                          size: 30,
                        ),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        // Use fullscreen dialog for immersive experience
        fullscreenDialog: true,
      ),
    );
  }
}
