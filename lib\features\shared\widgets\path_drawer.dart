import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cilas_biskra/core/constants.dart';

class PathDrawer extends StatefulWidget {
  final String currentPath;

  const PathDrawer({super.key, required this.currentPath});

  @override
  State<PathDrawer> createState() => _PathDrawerState();
}

class _PathDrawerState extends State<PathDrawer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Map sub-routes to their parent learning paths
  String _getParentPath(String currentPath) {
    // Holcim Academy family - includes all holcim-related routes
    if (currentPath.startsWith('/holcim') ||
        currentPath == AppRoute.holcimLeadershipCards ||
        currentPath == AppRoute.holcimLeadershipProgram ||
        currentPath == AppRoute.functionalAcademies ||
        currentPath == AppRoute.forumCard ||
        currentPath.contains('holcim')) {
      return AppRoute.holcimAcademy;
    }

    // Cement Academy family - includes all cement-related routes
    if (currentPath.startsWith('/cement') ||
        currentPath == AppRoute.cementDetails ||
        currentPath.contains('cement')) {
      return AppRoute.cementAcademy;
    }

    // Global Safety family
    if (currentPath.startsWith('/globalSafety') ||
        currentPath.contains('safety')) {
      return AppRoute.globalSafety;
    }

    // Soft Skills family (Percipio) - but exclude holcim leadership routes
    if ((currentPath.startsWith('/percipio') ||
            currentPath.contains('percipio') ||
            currentPath.contains('soft')) &&
        !currentPath.contains('holcim')) {
      return AppRoute.percipio;
    }

    // Language Skills family (GoFluent)
    if (currentPath.startsWith('/gofluent') ||
        currentPath.contains('language') ||
        currentPath.contains('fluent')) {
      return AppRoute.goFluent;
    }

    // Compliance family
    if (currentPath.startsWith('/compliance') ||
        currentPath.contains('compliance')) {
      return AppRoute.compliance;
    }

    // For other paths, return the path itself
    return currentPath;
  }

  // Define the paths with their icons and colors
  final List<Map<String, dynamic>> _paths = [
    {
      'title': 'Global Safety',
      'icon': FontAwesomeIcons.helmetSafety,
      'color': const Color(0xFF8CD44A),
      'route': AppRoute.globalSafety,
    },
    {
      'title': 'Cement Academy',
      'icon': Icons.local_library,
      'color': const Color.fromARGB(255, 12, 43, 90),
      'route': AppRoute.cementAcademy,
    },
    {
      'title': 'Holcim Academy',
      'icon': Icons.school,
      'color': const Color(0xFF0EA5E9),
      'route': AppRoute.holcimAcademy,
    },
    {
      'title': 'Soft Skills',
      'icon': Icons.people,
      'color': const Color(0xFF10B981),
      'route': AppRoute.percipio,
    },
    {
      'title': 'Language',
      'icon': Icons.translate,
      'color': const Color(0xFF8B5CF6),
      'route': AppRoute.goFluent,
    },

    {
      'title': 'Compliance',
      'icon': Icons.gavel,
      'color': const Color(0xFFEF4444),
      'route': AppRoute.compliance,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    // Start the animation immediately
    _animationController.forward();
  }

  @override
  void didUpdateWidget(PathDrawer oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Rebuild when currentPath changes to update selection state
    if (oldWidget.currentPath != widget.currentPath) {
      setState(() {
        // Force rebuild to update selection indicators
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 70,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 5,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 20),
          ..._paths.map((path) => _buildPathItem(path)),
          const SizedBox(height: 20),
          _buildHomeButton(),
        ],
      ),
    );
  }

  Widget _buildPathItem(Map<String, dynamic> path) {
    final String parentPath = _getParentPath(widget.currentPath);
    final bool isSelected = parentPath == path['route'];

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Tooltip(
          message: path['title'],
          verticalOffset: 20,
          preferBelow: true,
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                // Always allow navigation to the main learning path page
                // This allows users to return to the main page even when in sub-routes
                context.go(path['route']);
              },
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: 1.0),
                duration: Duration(
                  milliseconds: 300 + (_paths.indexOf(path) * 100),
                ),
                curve: Curves.easeOutBack,
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: (0.5 + (value * 0.5)).clamp(0.5, 1.0),
                    child: Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: Opacity(
                        opacity: value.clamp(0.0, 1.0),
                        child: Column(
                          children: [
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color:
                                    isSelected
                                        ? path['color'].withAlpha(50)
                                        : Colors.transparent,
                                shape: BoxShape.circle,
                                boxShadow:
                                    isSelected
                                        ? [
                                          BoxShadow(
                                            color: path['color'].withAlpha(100),
                                            blurRadius: 8,
                                            spreadRadius: 1,
                                          ),
                                        ]
                                        : [],
                                border: Border.all(
                                  color:
                                      isSelected
                                          ? path['color']
                                          : Colors.transparent,
                                  width: 2,
                                ),
                              ),
                              child: Icon(
                                path['icon'],
                                color:
                                    isSelected
                                        ? path['color']
                                        : Colors.grey[600],
                                size: 24,
                              ),
                            ),
                            const SizedBox(height: 4),
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              width: isSelected ? 6 : 0,
                              height: isSelected ? 6 : 0,
                              decoration: BoxDecoration(
                                color: path['color'],
                                shape: BoxShape.circle,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHomeButton() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Tooltip(
          message: "Return to Paths",
          verticalOffset: 20,
          preferBelow: true,
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                context.go(AppRoute.paths);
              },
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: 1.0),
                duration: const Duration(milliseconds: 800),
                curve: Curves.easeOutBack,
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: (0.5 + (value * 0.5)).clamp(0.5, 1.0),
                    child: Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: Opacity(
                        opacity: value.clamp(0.0, 1.0),
                        child: Column(
                          children: [
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.grey.withAlpha(30),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(20),
                                    blurRadius: 4,
                                    spreadRadius: 1,
                                  ),
                                ],
                                border: Border.all(
                                  color: Colors.grey.withAlpha(100),
                                  width: 1,
                                ),
                              ),
                              child: Icon(
                                Icons.home,
                                color: Colors.grey[700],
                                size: 24,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Home',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                color: Colors.grey[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
