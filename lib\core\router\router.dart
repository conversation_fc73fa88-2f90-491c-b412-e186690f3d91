import 'package:cilas_biskra/core/router/tansition_effect.dart';
import 'package:cilas_biskra/features/home/<USER>/nex_gen.dart/holcim_nexgen.dart';
import 'package:cilas_biskra/features/sections/cement_academy/screens/cement_academy.dart';
import 'package:cilas_biskra/features/sections/cement_academy/screens/cement_details.dart';
import 'package:cilas_biskra/features/sections/cement_academy/screens/last.dart';
import 'package:cilas_biskra/features/sections/global_safety/global_safety_screen.dart';
import 'package:cilas_biskra/features/paths/screens/paths_screen.dart';
import 'package:cilas_biskra/features/sections/compliance/screens/compliance.dart';
import 'package:cilas_biskra/features/sections/holcim_academy/functional_academies/functional_academies.dart';
import 'package:cilas_biskra/features/sections/holcim_academy/holcim_academy_screen.dart';
import 'package:cilas_biskra/features/sections/holcim_academy/leadership_cards/screens/leadership_cards.dart';
import 'package:cilas_biskra/features/sections/holcim_academy/leadership_program/leadership_developement_program.dart';
import 'package:cilas_biskra/features/sections/language/screens/gofluent.dart';
import 'package:cilas_biskra/features/home/<USER>/nex_gen.dart/splash.dart';
import 'package:cilas_biskra/features/sections/soft_skills/percipio/screens/percipio.dart';
import 'package:cilas_biskra/features/shared/widgets/path_wrapper.dart';
import 'package:go_router/go_router.dart';
import '../constants.dart';

final GoRouter router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      pageBuilder:
          (context, state) =>
              pageWithSwipeRightTransition(child: const Splash()),
    ),
    // GoRoute(
    //   path: AppRoute.newGenAcademy,
    //   pageBuilder:
    //       (context, state) => pageWithSwipeRightTransition(child: NewGenAcademy()),
    // ),
    GoRoute(
      path: AppRoute.paths,
      pageBuilder:
          (context, state) =>
              pageWithSwipeRightTransition(child: PathsScreen()),
    ),
    GoRoute(
      path: '${AppRoute.cementDetails}/:tab',
      pageBuilder: (context, state) {
        final tab = state.pathParameters['tab'] ?? '';
        return pageWithSwipeRightTransition(
          child: PathWrapper(
            currentPath: AppRoute.cementDetails,
            child: CementDetails(initialTab: tab),
          ),
        );
      },
    ),
    GoRoute(
      path: AppRoute.cementDetails,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.cementDetails,
              child: CementDetails(),
            ),
          ),
    ),
    GoRoute(
      path: '${AppRoute.plantLeadership}/:page',
      pageBuilder: (context, state) {
        final page = state.pathParameters['page'] ?? '';
        return pageWithSwipeRightTransition(
          child: PathWrapper(
            currentPath: AppRoute.plantLeadership,
            child: PlantLeadershipApp(initialPage: page),
          ),
        );
      },
    ),
    GoRoute(
      path: AppRoute.plantLeadership,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.plantLeadership,
              child: PlantLeadershipApp(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.globalSafety,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.globalSafety,
              child: GlobalSafetyScreen(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.goFluent,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.goFluent,
              child: Gofluent(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.percipio,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.percipio,
              child: PercipioShowcase(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.compliance,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.compliance,
              child: Compliance(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.cementAcademy,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.cementAcademy,
              child: CementAcademy(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.splash,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(child: Splash()),
    ),
    GoRoute(
      path: AppRoute.holcimNextgen,
      pageBuilder:
          (context, state) =>
              pageWithSwipeRightTransition(child: const HolcimNextGen()),
    ),
    GoRoute(
      path: AppRoute.holcimLeadershipCards,

      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.holcimLeadershipCards,
              child: LeadershipCards(program: state.extra.toString()),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.functionalAcademies,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.functionalAcademies,
              child: const FunctionalAcademies(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.holcimLeadershipProgram,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.holcimLeadershipProgram,
              child: const LeadershipDevelopmentScreen(),
            ),
          ),
    ),
    GoRoute(
      path: AppRoute.holcimAcademy,
      pageBuilder:
          (context, state) => pageWithSwipeRightTransition(
            child: PathWrapper(
              currentPath: AppRoute.holcimAcademy,
              child: const HolcimAcademyScreen(),
            ),
          ),
    ),
  ],
);
