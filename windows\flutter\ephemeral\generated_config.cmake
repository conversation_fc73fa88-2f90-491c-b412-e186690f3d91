# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "F:\\portfolio\\cilas_biskra" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter"
  "PROJECT_DIR=F:\\portfolio\\cilas_biskra"
  "FLUTTER_ROOT=C:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=F:\\portfolio\\cilas_biskra\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=F:\\portfolio\\cilas_biskra"
  "FLUTTER_TARGET=F:\\portfolio\\cilas_biskra\\lib/features/sections/cement_academy/screens/last.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=F:\\portfolio\\cilas_biskra\\.dart_tool\\package_config.json"
)
