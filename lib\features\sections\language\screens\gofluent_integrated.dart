import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class GoFluentIntegrated extends StatefulWidget {
  const GoFluentIntegrated({super.key});

  @override
  State<GoFluentIntegrated> createState() => _GoFluentIntegratedState();
}

class _GoFluentIntegratedState extends State<GoFluentIntegrated>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideUpAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _slideUpAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Icon name for the service cards
  final List<String> iconName = [
    'assets/gofluent/Corporate-Language-Academy-icon-1.svg',
    'assets/gofluent/Conversation-Classes-icon.svg',
    'assets/gofluent/download-_1_.svg',
    'assets/gofluent/Group-Lessons-Icon-1.svg',
    'assets/gofluent/Writing-Lessons-Icon.svg',
    'assets/gofluent/download.svg',
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          // App Bar
          FadeTransition(opacity: _fadeAnimation, child: _buildAppBar()),
          // Main Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Hero Section
                  _buildHeroSection(context),
                  // Services Grid
                  Row(
                    children: [
                      SizedBox(
                        height: 500,
                        child: Image.asset(
                          'assets/images/Language-Training-Solutions-image-1.png',
                          fit: BoxFit.fill,
                        ),
                      ),
                      const SizedBox(width: 24),
                      _buildServicesGrid(context),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      height: 64,
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        children: [
          // Logo
          SizedBox(
            width: 200,
            child: SvgPicture.asset(
              'assets/gofluent/goFLUENT-logo.svg',
              colorFilter: const ColorFilter.mode(
                Color(0xFF0F3A4D),
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 48, vertical: 48),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Desktop layout
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side content
              Expanded(
                flex: 5,
                child: AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, _slideUpAnimation.value),
                      child: child,
                    );
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Accelerate\nLanguage\nLearning',
                        style: TextStyle(
                          fontSize: 48,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0F3A4D),
                          height: 1.1,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'The world\'s #1 provider of integrated language training solutions for the enterprise',
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xFF0F3A4D),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Right side flowchart
              Expanded(
                flex: 5,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    height: 250,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Image.asset(
                      'assets/gofluent/images/Portal-Banner-updated.webp',
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildServicesGrid(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Integrated Language Training Solutions',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Color(0xFF0F3A4D),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Transform your corporate language training approach. Deploy a global language strategy. Achieve results and higher ROI.',
              style: TextStyle(fontSize: 16, color: Color(0xFF0F3A4D)),
            ),
            const SizedBox(height: 32),
            LayoutBuilder(
              builder: (context, constraints) {
                final int columns =
                    constraints.maxWidth > 900
                        ? 3
                        : (constraints.maxWidth > 600 ? 2 : 1);
                return GridView.count(
                  crossAxisCount: columns,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.5, // More rectangular shape
                  children: [
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: child,
                          ),
                        );
                      },
                      child: _buildServiceCard(
                        'Language Academy',
                        'Deliver online language training across your global organization',
                        Colors.blue,
                        iconName[0],
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: child,
                          ),
                        );
                      },
                      child: _buildServiceCard(
                        'Conversation Classes',
                        'Enhance language skills in virtual conversation classes',
                        Colors.green,
                        iconName[1],
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: child,
                          ),
                        );
                      },
                      child: _buildServiceCard(
                        'Individual Lessons',
                        'Optimize training with individual virtual language lessons',
                        Colors.orange,
                        iconName[2],
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: child,
                          ),
                        );
                      },
                      child: _buildServiceCard(
                        'Group Lessons',
                        'Connect employees worldwide through virtual group language lessons',
                        Colors.purple,
                        iconName[3],
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: child,
                          ),
                        );
                      },
                      child: _buildServiceCard(
                        'Writing Lessons',
                        'Unlock effective business writing in any language',
                        Colors.teal,
                        iconName[4],
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: child,
                          ),
                        );
                      },

                      child: _buildServiceCard(
                        'Face-to-Face Language Training',
                        'Blend face-to-face language training with your digital platform',
                        Colors.amber,
                        iconName[5],
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceCard(
    String title,
    String description,
    Color iconColor,
    String icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // Use minimum height
        children: [
          // Icon Placeholder
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: iconColor.withAlpha(
                50,
              ), // Using withAlpha instead of withOpacity
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(child: SvgPicture.asset(icon)),
          ),
          const SizedBox(height: 12),
          // Title
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF0F3A4D),
            ),
          ),
          const SizedBox(height: 8),
          // Description
          Expanded(
            child: Text(
              description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            ),
          ),
          // Discover Button
          Row(
            mainAxisSize: MainAxisSize.min, // Use minimum space
            children: [
              Flexible(
                child: Text(
                  'Discover $title',
                  overflow: TextOverflow.ellipsis, // Handle text overflow
                  maxLines: 1,
                  style: TextStyle(
                    color: const Color(0xFF0F3A4D),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_forward,
                color: Color(0xFF0F3A4D),
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
