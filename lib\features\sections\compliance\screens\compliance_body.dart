import 'dart:io';

import 'package:flutter/material.dart';
import 'package:cilas_biskra/core/helpers/pdf_loader.dart';
import 'package:cilas_biskra/core/helpers/pdf_helper.dart';

class ComplianceBody extends StatefulWidget {
  const ComplianceBody({super.key});

  @override
  State<ComplianceBody> createState() => _ComplianceBodyState();
}

class _ComplianceBodyState extends State<ComplianceBody>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final AnimationController _animationController;
  late final Animation<double> _fadeInAnimation;
  late final Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabSelection);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 0.7, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.blue],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: const BoxDecoration(
                          color: Color(0xFF0A5F73),
                          shape: BoxShape.circle,
                        ),
                        child: const Center(
                          child: Text(
                            'H',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Text(
                        'Holcim Compliance Training',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0A5F73),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Expanded(
              child: Center(
                child: SizedBox(
                  width: size.width * 0.85,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FadeTransition(
                        opacity: _fadeInAnimation,
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(
                                  0xFF0A5F73,
                                ).withAlpha((0.85 * 255).round()),
                                const Color(
                                  0xFF20B2AA,
                                ).withAlpha((0.85 * 255).round()),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(
                                  (0.2 * 255).round(),
                                ),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: const Icon(
                                      Icons.supervisor_account_rounded,
                                      color: Color(0xFF0A5F73),
                                      size: 36,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  const Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Compliance Training Overview',
                                          style: TextStyle(
                                            fontSize: 28,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                        SizedBox(height: 8),
                                        Text(
                                          'At Holcim, compliance isn\'t just a checkbox — it\'s a mindset.',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 24),
                              const Text(
                                'Training focuses on reinforcing ethical decision-making, understanding core values, and applying integrity in daily operations. It\'s tailored to employee roles and evolves over time to refresh and deepen knowledge.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(
                                (0.05 * 255).round(),
                              ),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: TabBar(
                          controller: _tabController,
                          indicator: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: const LinearGradient(
                              colors: [Color(0xFF0A5F73), Color(0xFF20B2AA)],
                            ),
                          ),
                          labelColor: Colors.white,
                          unselectedLabelColor: const Color(0xFF0A5F73),
                          tabs: const [
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.book),
                                  SizedBox(width: 8),
                                  Text('Code of Conduct'),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.shield),
                                  SizedBox(width: 8),
                                  Text('Anti-Bribery'),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.card_giftcard),
                                  SizedBox(width: 8),
                                  Text('Gifts'),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.balance),
                                  SizedBox(width: 8),
                                  Text('Conflicts'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return TabBarView(
                              controller: _tabController,
                              children: [
                                AnimatedBuilder(
                                  animation: _animationController,
                                  builder: (context, child) {
                                    return FadeTransition(
                                      opacity: _fadeInAnimation,
                                      child: ScaleTransition(
                                        scale: _scaleAnimation,
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: TrainingTabContent(
                                    title: 'Code of Business Conduct Training',
                                    color: const Color(0xFF00A651),
                                    icon: Icons.book,
                                    forWho:
                                        'All Holcim employees, especially those with over 3 years of tenure.',
                                    whyMatters:
                                        'Helps employees recognize ethical situations and act confidently, consistently with company values.',
                                    topics: [
                                      'Holcim\'s core values and behaviors',
                                      'Respect, integrity, and inclusion',
                                      'Preventing harassment or discrimination',
                                      'Disclosing conflicts of interest',
                                      'Promoting fair competition',
                                      'Managing gifts and hospitality',
                                      'Recognizing bribery',
                                      'Reporting misconduct safely',
                                    ],
                                  ),
                                ),
                                AnimatedBuilder(
                                  animation: _animationController,
                                  builder: (context, child) {
                                    return FadeTransition(
                                      opacity: _fadeInAnimation,
                                      child: ScaleTransition(
                                        scale: _scaleAnimation,
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: TrainingTabContent(
                                    title: 'Anti-Bribery and Corruption',
                                    color: const Color(0xFFFFB612),
                                    icon: Icons.shield,
                                    forWho:
                                        'Risk-exposed roles: ExCo, Sales, Procurement\nRisk mitigators: Legal, Compliance, Audit',
                                    whyMatters:
                                        'Equip employees with tools to detect and avoid bribery risks, especially in high-stakes roles or markets.',
                                    topics: [
                                      'What counts as a bribe',
                                      'Identifying government officials',
                                      'Risks of third-party intermediaries',
                                      'Legal gift giving/receiving',
                                      'Spotting red flags',
                                      'Reporting concerns',
                                    ],
                                  ),
                                ),
                                AnimatedBuilder(
                                  animation: _animationController,
                                  builder: (context, child) {
                                    return FadeTransition(
                                      opacity: _fadeInAnimation,
                                      child: ScaleTransition(
                                        scale: _scaleAnimation,
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: TrainingTabContent(
                                    title: 'Giving and Receiving Gifts',
                                    color: const Color(0xFFFF6B6B),
                                    icon: Icons.card_giftcard,
                                    forWho: 'Same as anti-bribery group',
                                    whyMatters:
                                        'Set clear boundaries on what\'s okay to give/receive to prevent unintended influence.',
                                    topics: [
                                      'Prevent unintended influence',
                                      'Protect business reputation',
                                      'Understand gift policies',
                                      'Record gifts properly',
                                      'Identify inappropriate gifts',
                                      'Decline gifts politely',
                                    ],
                                  ),
                                ),
                                AnimatedBuilder(
                                  animation: _animationController,
                                  builder: (context, child) {
                                    return FadeTransition(
                                      opacity: _fadeInAnimation,
                                      child: ScaleTransition(
                                        scale: _scaleAnimation,
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: TrainingTabContent(
                                    title: 'Managing Conflicts of Interest',
                                    color: const Color(0xFF6772E5),
                                    icon: Icons.balance,
                                    forWho: 'Same as anti-bribery group',
                                    whyMatters:
                                        'Identify personal interests that could affect business decisions to maintain trust.',
                                    topics: [
                                      'Reduce bias',
                                      'Support transparency',
                                      'Maintain trust',
                                      'Identify conflicts',
                                      'Disclosure procedures',
                                      'Mitigation strategies',
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class TrainingTabContent extends StatelessWidget {
  final String title;
  final Color color;
  final IconData icon;
  final String forWho;
  final String whyMatters;
  final List<String> topics;

  const TrainingTabContent({
    super.key,
    required this.title,
    required this.color,
    required this.icon,
    required this.forWho,
    required this.whyMatters,
    required this.topics,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 6,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha((0.05 * 255).round()),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: color.withAlpha((0.1 * 255).round()),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Icon(icon, color: color, size: 28),
                      ),
                      const SizedBox(width: 16),
                      Flexible(
                        child: Text(
                          title,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Who it\'s for:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        forWho,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black54,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Why it matters:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        whyMatters,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Key Topics:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TopicsGrid(topics: topics, color: color),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          flex: 3,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha((0.05 * 255).round()),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ListView(
              // Use a smaller padding to ensure content fits
              padding: EdgeInsets.zero,
              children: [
                const Text(
                  'Training Resources',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),
                ResourceCard(
                  tabTitle: title,
                  title: 'Interactive Course',
                  description: 'Self-paced online learning',
                  icon: Icons.laptop_mac,
                  color: color,
                ),
                const SizedBox(height: 12),
                ResourceCard(
                  tabTitle: title,
                  title: 'Reference Guide',
                  description: 'Downloadable PDF',
                  icon: Icons.description,
                  color: color,
                ),
                const SizedBox(height: 12),
                ResourceCard(
                  tabTitle: title,
                  title: 'Discussion Forum',
                  description: 'Connect with colleagues',
                  icon: Icons.forum,
                  color: color,
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withAlpha((0.1 * 255).round()),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.calendar_today, color: color),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              'Upcoming Sessions',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: color,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'May 20, 2025 - Virtual Workshop',
                        style: TextStyle(fontSize: 14, color: Colors.black87),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      const Text(
                        'June 5, 2025 - In-person Training',
                        style: TextStyle(fontSize: 14, color: Colors.black87),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      const Text(
                        'June 25, 2025 - Q&A Session',
                        style: TextStyle(fontSize: 14, color: Colors.black87),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class ResourceCard extends StatefulWidget {
  final String tabTitle;
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const ResourceCard({
    super.key,
    required this.tabTitle,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });

  @override
  State<ResourceCard> createState() => _ResourceCardState();
}

class _ResourceCardState extends State<ResourceCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTap: () async {
          File pdfFile;
          if (widget.title == 'Reference Guide') {
            // Open the PDF in fullscreen mode
            if (widget.tabTitle == 'Code of Business Conduct Training') {
              pdfFile = await PdfLoader.loadPDF(
                'assets/compliance/Directive Decisions with Integrity _ Conflict of Interest 2019.pdf',
              );
            } else if (widget.tabTitle == 'Anti-Bribery and Corruption') {
              pdfFile = await PdfLoader.loadPDF(
                'assets/compliance/Policy Anti-Bribery and Corruption 2023.pdf',
              );
            } else if (widget.tabTitle == 'Giving and Receiving Gifts') {
              pdfFile = await PdfLoader.loadPDF(
                'assets/compliance/Directive Gifts Hospitality Entertainment and Travel 2019.pdf',
              );
            } else {
              pdfFile = await PdfLoader.loadPDF(
                'assets/compliance/CoBC 2024 - EN.pdf',
              );
            }
            if (context.mounted) {
              PDFHelper.openPDFInFullscreen(context, pdfFile);
            }
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: widget.color.withAlpha((0.2 * 255).round()),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: widget.color.withAlpha((0.1 * 255).round()),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(widget.icon, color: widget.color, size: 20),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.title == 'Reference Guide')
                Icon(Icons.arrow_forward_ios, color: Colors.black26, size: 14),
            ],
          ),
        ),
      ),
    );
  }
}

class TopicsGrid extends StatelessWidget {
  final List<String> topics;
  final Color color;

  const TopicsGrid({super.key, required this.topics, required this.color});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate how many items to show per row based on available width
        final crossAxisCount = constraints.maxWidth < 500 ? 1 : 2;

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: 4.0,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            // Limit the maximum height by setting a max extent
            mainAxisExtent: 50,
          ),
          itemCount: topics.length,
          itemBuilder: (context, index) {
            return TopicItem(topic: topics[index], color: color, index: index);
          },
        );
      },
    );
  }
}

class TopicItem extends StatefulWidget {
  final String topic;
  final Color color;
  final int index;

  const TopicItem({
    super.key,
    required this.topic,
    required this.color,
    required this.index,
  });

  @override
  State<TopicItem> createState() => _TopicItemState();
}

class _TopicItemState extends State<TopicItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: widget.color.withAlpha((0.15 * 255).round()),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: widget.color.withAlpha(((0.3) * 255).round()),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.05 * 255).round()),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
        shape: BoxShape.rectangle,
      ),
      child: Row(
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: widget.color.withAlpha(((0.1) * 255).round()),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(Icons.check_circle, color: widget.color, size: 18),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.topic,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black54,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
