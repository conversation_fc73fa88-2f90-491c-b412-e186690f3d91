import 'package:cilas_biskra/features/sections/language/screens/gofluent_official.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class GoFluentGeneral extends StatefulWidget {
  const GoFluentGeneral({super.key});

  @override
  State<GoFluentGeneral> createState() => _GoFluentGeneralState();
}

class _GoFluentGeneralState extends State<GoFluentGeneral>
    with TickerProviderStateMixin {
  bool viewServices = false;
  late AnimationController _statsAnimationController;
  int _currentPage = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _statsAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..forward();
  }

  @override
  void dispose() {
    _statsAnimationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // Method to reset and restart animations
  void _resetAnimations() {
    // Reset the animation controller to the beginning
    _statsAnimationController.reset();
    // Start the animation again
    _statsAnimationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            color: const Color(0xFF006B82),
            height: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  height: 60,
                  width: 60,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Center(
                    child: Text(
                      'Go',
                      style: TextStyle(
                        color: Color(0xFF006B82),
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                _buildNavItem(Icons.home, 'Home', 0),
                const SizedBox(width: 20),
                _buildNavItem(Icons.g_mobiledata, 'inegrated', 1),
                const SizedBox(width: 20),
              ],
            ),
          ),
          Expanded(
            child: GestureDetector(
              onHorizontalDragUpdate: (details) {
                if (details.delta.dx > 0) {
                  // Swipe right
                  if (_currentPage > 0) {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOut,
                    );
                    _resetAnimations();
                  }
                } else if (details.delta.dx < 0) {
                  // Swipe left
                  if (_currentPage < 1) {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOut,
                    );
                    _resetAnimations();
                  }
                }
              },
              child: PageView(
                physics: const BouncingScrollPhysics(),
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                    _resetAnimations();
                  });
                },
                children: [GoFluentOfficial(), _buildHolcimGoFluentPage()],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, int index) {
    return InkWell(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 500),
          curve: Curves.fastEaseInToSlowEaseOut,
        );
        // Reset animations when navigating to a new page
        _resetAnimations();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color:
                  _currentPage == index
                      ? Theme.of(context).colorScheme.secondary
                      : Colors.transparent,
              width: 4,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Icon(
                icon,
                color: _currentPage == index ? Colors.white : Colors.white70,
                size: 28,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: _currentPage == index ? Colors.white : Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHolcimGoFluentPage() {
    return Stack(
      children: [
        Positioned.fill(
          child: CustomPaint(painter: GoFluentWaveBackgroundPainter()),
        ),
        SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ShaderMask(
                          shaderCallback: (Rect bounds) {
                            return const LinearGradient(
                              colors: [
                                Color(0xFF006B82),
                                Color(0xFF8CC152),
                                Color(0xFFE3B505),
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ).createShader(bounds);
                          },
                          blendMode: BlendMode.srcIn,
                          child: Text(
                            'Welcome to goFLUENT @ Holcim',
                            style: TextStyle(
                              fontFamily: 'CalSans',
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Unlock Your Voice. Grow Your Career. Go Global.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 40),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildAnimatedStatCard(
                      icon: Icons.people,
                      color: const Color(0xFF006B82),
                      title: 'Employees registered',
                      value: '9,745',
                      subtitle: '32.48% of total employees',
                      index: 0,
                    ),
                    _buildAnimatedStatCard(
                      icon: Icons.rocket,
                      color: const Color(0xFFE3B505),
                      title: 'Hours spent learning',
                      value: '46,986',
                      subtitle: '',
                      index: 1,
                    ),
                    _buildAnimatedStatCard(
                      icon: Icons.school_outlined,
                      color: const Color(0xFF8CC152),
                      title: 'Learning activities',
                      value: '58,548',
                      subtitle: 'completed',
                      index: 2,
                    ),
                    _buildAnimatedStatCard(
                      icon: Icons.language,
                      color: const Color(0xFF006B82),
                      title: 'Languages learned',
                      value: '13',
                      subtitle: 'across Holcim global',
                      index: 3,
                    ),
                  ],
                ),
                const SizedBox(height: 40),
                _buildGettingStartedTab(),
                const SizedBox(height: 40),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _buildTopLanguagesCard()),
                    const SizedBox(width: 24),
                    Expanded(child: _buildTopRegionsCard()),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnimatedStatCard({
    required IconData icon,
    required Color color,
    required String title,
    required String value,
    required String subtitle,
    required int index,
  }) {
    final Animation<double> animation = CurvedAnimation(
      parent: _statsAnimationController,
      curve: Interval(index * 0.2, 0.2 + index * 0.2, curve: Curves.easeOut),
    );

    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.scale(
          scale: animation.value,
          child: Opacity(
            opacity: animation.value,
            child: Container(
              width: 220,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
                border: Border.all(
                  color: color.withAlpha(51),
                  width: 2,
                ), // 0.2 * 255 = 51
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: color.withAlpha(26), // 0.1 * 255 = 26
                          shape: BoxShape.circle,
                        ),
                        child: Icon(icon, color: color),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          title,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  if (subtitle.isNotEmpty)
                    Text(
                      subtitle,
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGettingStartedTab() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Get Started in 4 Simple Steps',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF006B82),
                  ),
                ),
                const SizedBox(height: 32),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStepCircle(
                      number: '1',
                      title: 'Set up your Profile',
                      isActive: true,
                    ),
                    _buildStepConnector(isActive: true),
                    _buildStepCircle(
                      number: '2',
                      title: 'Take the Proficiency Test',
                      isActive: true,
                    ),
                    _buildStepConnector(isActive: false),
                    _buildStepCircle(
                      number: '3',
                      title: 'Start learning!',
                      isActive: false,
                    ),
                    _buildStepConnector(isActive: false),
                    _buildStepCircle(
                      number: '4',
                      title: 'Explore the Language Academy',
                      isActive: false,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          flex: 2,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  const Color(0xFF006B82),
                  const Color(0xFF006B82).withAlpha(204), // 0.8 * 255 = 204
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                const Text(
                  'Scan the QR code to access goFLUENT',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            'Android',
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                          Image.asset('assets/images/android_qrcode.png'),
                        ],
                      ),
                    ),
                    SizedBox(width: 24),
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            'iOS',
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                          Image.asset('assets/images/ios_qrcode.png'),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                const Text(
                  '=> Note: GoFluent is only accessible through Percipio on iOS. This is why we recommend using GoFluent on an Android device or desktop.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStepCircle({
    required String number,
    required String title,
    required bool isActive,
  }) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color:
                isActive
                    ? const Color(0xFF006B82)
                    : Colors.grey.withAlpha(51), // 0.2 * 255 = 51
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number,
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: 100,
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: isActive ? const Color(0xFF006B82) : Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStepConnector({required bool isActive}) {
    return Container(
      width: 40,
      height: 2,
      color:
          isActive
              ? const Color(0xFF006B82)
              : Colors.grey.withAlpha(51), // 0.2 * 255 = 51
    );
  }

  Widget _buildTopLanguagesCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Top 5 Languages Studied',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF006B82),
            ),
          ),
          const SizedBox(height: 24),
          _buildLanguageProgressBar('English', 0.85, Colors.blue),
          const SizedBox(height: 16),
          _buildLanguageProgressBar('German', 0.65, Colors.green),
          const SizedBox(height: 16),
          _buildLanguageProgressBar('French', 0.55, Colors.orange),
          const SizedBox(height: 16),
          _buildLanguageProgressBar('Spanish', 0.45, Colors.red),
          const SizedBox(height: 16),
          _buildLanguageProgressBar('Portuguese (BR)', 0.35, Colors.purple),
        ],
      ),
    );
  }

  Widget _buildLanguageProgressBar(String language, double value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              language,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            Text(
              '${(value * 100).toInt()}%',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: value,
          backgroundColor: Colors.grey.withAlpha(51), // 0.2 * 255 = 51
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }

  Widget _buildTopRegionsCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Top Business Units by Learning Hours',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF006B82),
            ),
          ),
          const SizedBox(height: 70),
          SizedBox(
            height: 120,
            child: CustomPaint(
              size: const Size(double.infinity, 120),
              painter: RegionsChartPainter(),
            ),
          ),
          const SizedBox(height: 70),
          Wrap(
            spacing: 24,
            runSpacing: 8,
            children: [
              _buildLegendItem('Colombia', const Color(0xFFE3B505)),
              _buildLegendItem('Mexico', const Color(0xFF006B82)),
              _buildLegendItem('Algeria', const Color(0xFF8CC152)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(label, style: const TextStyle(fontSize: 14)),
      ],
    );
  }
}

class StepConnectorPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint =
        Paint()
          ..color = const Color(0xFF006B82)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

    final Path path1 =
        Path()
          ..moveTo(140, 110)
          ..quadraticBezierTo(200, 130, 250, 180);
    canvas.drawPath(path1, paint);

    final Path path2 =
        Path()
          ..moveTo(290, 200)
          ..quadraticBezierTo(350, 230, 400, 250);
    canvas.drawPath(path2, paint);

    final Path path3 =
        Path()
          ..moveTo(450, 250)
          ..quadraticBezierTo(525, 200, 600, 150);
    canvas.drawPath(path3, paint);

    _drawArrow(canvas, Offset(250, 180), -45, paint);
    _drawArrow(canvas, Offset(400, 250), -15, paint);
    _drawArrow(canvas, Offset(600, 150), 120, paint);
  }

  void _drawArrow(Canvas canvas, Offset position, double angle, Paint paint) {
    canvas.save();
    canvas.translate(position.dx, position.dy);
    canvas.rotate(angle * math.pi / 180);

    final Path arrowPath =
        Path()
          ..moveTo(0, 0)
          ..lineTo(-10, -5)
          ..lineTo(-10, 5)
          ..close();

    canvas.drawPath(arrowPath, paint..style = PaintingStyle.fill);
    canvas.restore();
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class RegionsChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final double radius = size.width / 5;
    final Offset center = Offset(size.width / 2, size.height / 2);

    _drawPieSegment(
      canvas,
      center,
      radius,
      0,
      120,
      const Color(0xFFE3B505),
      'Colombia',
    );

    _drawPieSegment(
      canvas,
      center,
      radius,
      120,
      100,
      const Color(0xFF006B82),
      'Mexico',
    );

    _drawPieSegment(
      canvas,
      center,
      radius,
      220,
      140,
      const Color(0xFF8CC152),
      'Algeria',
    );
  }

  void _drawPieSegment(
    Canvas canvas,
    Offset center,
    double radius,
    double startAngle,
    double sweepAngle,
    Color color,
    String label,
  ) {
    final Paint paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final double startRad = startAngle * math.pi / 180;
    final double sweepRad = sweepAngle * math.pi / 180;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startRad,
      sweepRad,
      true,
      paint,
    );

    final double midAngle = startRad + sweepRad / 2;
    final double labelRadius = radius * 0.65;
    final Offset labelPosition = Offset(
      center.dx + labelRadius * math.cos(midAngle),
      center.dy + labelRadius * math.sin(midAngle),
    );

    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10, // Reduced from 14 to 10 to match the smaller chart size
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();

    textPainter.paint(
      canvas,
      Offset(
        labelPosition.dx - textPainter.width / 2,
        labelPosition.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class GoFluentWaveBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint =
        Paint()
          ..shader = LinearGradient(
            colors: [
              Color(0xFFFFC107), // Vibrant yellow like in the image
              Color(0xFFFFD54F), // Lighter yellow
              Color(0xFFFFF176), // Even lighter yellow
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
          ..style = PaintingStyle.fill;

    final Path path = Path();

    // Start from bottom-left
    path.moveTo(0, size.height);

    // Create the main wave curve flowing from bottom-left to top-right
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.6,
      size.width * 0.45,
      size.height * 0.65,
    );

    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.7,
      size.width * 0.85,
      size.height * 0.4,
    );

    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.2,
      size.width,
      size.height * 0.1,
    );

    // Connect to corners
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
