#include <flutter/dart_project.h>
#include <flutter/flutter_view_controller.h>
#include <windows.h>
#include "flutter_window.h"
#include "utils.h"

int APIENTRY wWinMain(_In_ HINSTANCE instance, _In_opt_ HINSTANCE prev,
                      _In_ wchar_t *command_line, _In_ int show_command) {
  ::ShowWindow(::GetConsoleWindow(), SW_HIDE);

  // Optional console handling during debugging
  if (!::AttachConsole(ATTACH_PARENT_PROCESS) && ::IsDebuggerPresent()) {
    CreateAndAttachConsole();
  }

  ::CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);

  flutter::DartProject project(L"data");

  std::vector<std::string> command_line_arguments =
      GetCommandLineArguments();
  project.set_dart_entrypoint_arguments(std::move(command_line_arguments));

  FlutterWindow window(project);

  // Get the screen dimensions for fullscreen
  RECT desktop;
  const HWND hDesktop = GetDesktopWindow();
  GetWindowRect(hDesktop, &desktop);
  int screenWidth = desktop.right;
  int screenHeight = desktop.bottom;

  Win32Window::Point origin(0, 0);
  Win32Window::Size size(screenWidth, screenHeight);

  // Create window without decorations
  if (!window.Create(L"cilas_biskra", origin, size, true)) {
    return EXIT_FAILURE;
  }

  // Show the window in fullscreen
  window.Show();

  // Set to fullscreen
  SetWindowLong(window.GetHandle(), GWL_STYLE,
                GetWindowLong(window.GetHandle(), GWL_STYLE) & ~WS_OVERLAPPEDWINDOW);
  SetWindowPos(window.GetHandle(), HWND_TOP, 0, 0, screenWidth, screenHeight,
               SWP_SHOWWINDOW);

  window.SetQuitOnClose(true);

  ::MSG msg;
  while (::GetMessage(&msg, nullptr, 0, 0)) {
    ::TranslateMessage(&msg);
    ::DispatchMessage(&msg);
  }

  ::CoUninitialize();
  return EXIT_SUCCESS;
}
