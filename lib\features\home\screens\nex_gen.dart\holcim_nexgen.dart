import 'dart:async';
import 'package:cilas_biskra/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class HolcimNextGen extends StatefulWidget {
  const HolcimNextGen({super.key});

  @override
  State<HolcimNextGen> createState() => _HolcimNextGenState();
}

class _HolcimNextGenState extends State<HolcimNextGen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _gradientController;
  late Animation<double> _fadeInAnimation;
  late Animation<Alignment> _gradientBeginAnimation;
  late Animation<Alignment> _gradientEndAnimation;
  String _displayedHeadline = '';
  int _headlineIndex = 0;
  final String _fullHeadline = "HOLCIM UNLOCKS VALUE WITH NEXTGEN GROWTH 2030";
  final List<bool> _bulletPointVisible = [false, false, false, false, false];
  bool _isHeadlineComplete = false;

  @override
  void initState() {
    super.initState();
    // Initialize fade animation controller
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeInAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    // Initialize gradient animation controller
    _gradientController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    );

    // Create animations for the gradient positions
    _gradientBeginAnimation = Tween<Alignment>(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    ).animate(
      CurvedAnimation(parent: _gradientController, curve: Curves.easeInOut),
    );

    _gradientEndAnimation = Tween<Alignment>(
      begin: Alignment.centerRight,
      end: Alignment.centerLeft,
    ).animate(
      CurvedAnimation(parent: _gradientController, curve: Curves.easeInOut),
    );

    _fadeController.forward();

    // Typewriter animation for headline
    Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (mounted) {
        if (_headlineIndex < _fullHeadline.length) {
          setState(() {
            _displayedHeadline += _fullHeadline[_headlineIndex];
            _headlineIndex++;
          });
        } else {
          timer.cancel();
          // Set headline complete flag and start gradient animation
          setState(() {
            _isHeadlineComplete = true;
          });

          // Start the gradient animation
          _gradientController.repeat(reverse: true);

          // Start bullet point animations
          for (int i = 0; i < _bulletPointVisible.length; i++) {
            Future.delayed(Duration(milliseconds: i * 300), () {
              if (mounted) {
                setState(() {
                  _bulletPointVisible[i] = true;
                });
              }
            });
          }
        }
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _gradientController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final holcimBlue = const Color(0xFF006B82),
        holcimGreen = Color(0xFF97C93D),
        holcimYellow = Color(0xFF7ECA9C),
        holcimPurple = Color(0xFF0098D8);

    return GestureDetector(
      onHorizontalDragEnd: (details) {
        if (details.primaryVelocity! < 0) {
          context.go(
            AppRoute.paths,
          ); // Navigate to the next screen on swipe left
        } else if (details.primaryVelocity! > 0) {
          context.go(
            AppRoute.splash,
          ); // Navigate to the previous screen on swipe right
        }
      },
      child: Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            // Background image with darker overlay
            Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: const AssetImage(
                    'assets/images/beautiful-architecture-office-business-building-with-glass-window-shape.jpg',
                  ),
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(
                    Colors.black.withAlpha(153), // 0.6 * 255 = 153
                    BlendMode.multiply,
                  ),
                ),
              ),
            ),
            FadeTransition(
              opacity: _fadeInAnimation,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20),
                  // Main content
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Image.asset('assets/icons/logo_holcim_group.png'),
                          const SizedBox(height: 20),
                          // Headline with typewriter animation
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              gradient: LinearGradient(
                                colors: [
                                  Colors.black.withAlpha(
                                    128,
                                  ), // 0.5 * 255 = 128
                                  Colors.black.withAlpha(77), // 0.3 * 255 = 77
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                            ),
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                // Get the available width for better gradient sizing
                                final availableWidth = constraints.maxWidth;

                                return AnimatedBuilder(
                                  animation: _gradientController,
                                  builder: (context, child) {
                                    return Text(
                                      _displayedHeadline,
                                      style:
                                          _isHeadlineComplete
                                              ? TextStyle(
                                                fontSize: 40,
                                                fontWeight: FontWeight.bold,
                                                height: 1.2,
                                                foreground:
                                                    Paint()
                                                      ..shader = LinearGradient(
                                                        colors: [
                                                          holcimGreen,
                                                          holcimBlue,
                                                          holcimYellow,
                                                          holcimPurple,
                                                          Colors.white,
                                                        ],
                                                        begin:
                                                            _gradientBeginAnimation
                                                                .value,
                                                        end:
                                                            _gradientEndAnimation
                                                                .value,
                                                      ).createShader(
                                                        Rect.fromLTWH(
                                                          0,
                                                          0,
                                                          availableWidth,
                                                          100,
                                                        ),
                                                      ),
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black
                                                        .withAlpha(
                                                          153,
                                                        ), // 0.6 * 255 = 153
                                                    offset: const Offset(2, 2),
                                                    blurRadius: 4,
                                                  ),
                                                ],
                                              )
                                              : TextStyle(
                                                fontSize: 40,
                                                fontWeight: FontWeight.bold,
                                                height: 1.2,
                                                color: Colors.white,
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black
                                                        .withAlpha(
                                                          153,
                                                        ), // 0.6 * 255 = 153
                                                    offset: const Offset(2, 2),
                                                    blurRadius: 4,
                                                  ),
                                                ],
                                              ),
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 40),
                          // Content section with bullet points and sidebar
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Sidebar with image
                              Expanded(
                                child: Container(
                                  width: width * 0.3,
                                  height: height * 0.6,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage(
                                        'assets/holcimacademy/HOLCIM_ACADEMY_LOGO.f70d1dafa613.png',
                                      ),
                                      fit: BoxFit.cover,
                                      colorFilter: ColorFilter.mode(
                                        Colors.black.withAlpha(
                                          102,
                                        ), // 0.4 * 255 = 102
                                        BlendMode.dstIn,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 40),
                              // Main content area
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Bullet points in a fixed section - using ConstrainedBox to limit height
                                    AnimatedOpacity(
                                      opacity:
                                          _bulletPointVisible[0] ? 1.0 : 0.0,
                                      duration: const Duration(
                                        milliseconds: 500,
                                      ),
                                      child: _buildBulletPoint(
                                        "MAKE HOLCIM BEST WORKPLACE FOR OUR PEOPLE",
                                        holcimBlue,
                                      ),
                                    ),
                                    const SizedBox(height: 24),
                                    AnimatedOpacity(
                                      opacity:
                                          _bulletPointVisible[1] ? 1.0 : 0.0,
                                      duration: const Duration(
                                        milliseconds: 500,
                                      ),
                                      child: _buildBulletPoint(
                                        "TALENT IS NURTURED? DIVERSITY IS CELEBRATED & EMPLOYES ARE ENGAGED",
                                        holcimGreen,
                                      ),
                                    ),
                                    const SizedBox(height: 24),
                                    AnimatedOpacity(
                                      opacity:
                                          _bulletPointVisible[2] ? 1.0 : 0.0,
                                      duration: const Duration(
                                        milliseconds: 500,
                                      ),
                                      child: _buildBulletPoint(
                                        "EMBED A CULTURE OF PERFORMANCE, INNOVATION & EXCELLENCE",
                                        holcimYellow,
                                      ),
                                    ),
                                    const SizedBox(height: 24),
                                    AnimatedOpacity(
                                      opacity:
                                          _bulletPointVisible[3] ? 1.0 : 0.0,
                                      duration: const Duration(
                                        milliseconds: 500,
                                      ),
                                      child: _buildBulletPoint(
                                        "MOBILIZE EVERYONE BEHIND HOLCIM SPIRIT",
                                        holcimPurple,
                                      ),
                                    ),
                                    SizedBox(
                                      height: height * 0.25,
                                      child: Image.asset(
                                        'assets/images/decarbonize_without_promise.png',
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: color,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8),
            height: 8,
            width: 8,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w500,
                height: 1.5,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black.withAlpha(128),
                    offset: const Offset(1, 1),
                    blurRadius: 3,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
