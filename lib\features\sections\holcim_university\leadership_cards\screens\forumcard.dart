import 'package:flutter/material.dart';

class ForumCard extends StatelessWidget {
  ForumCard({super.key});

  Widget _buildSection(String title, String content, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF0F3A4D),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withValues(alpha: 0.2)),
          ),
          child: Text(
            content,
            style: TextStyle(
              fontSize: 16,
              height: 1.5,
              color: Colors.grey.shade800,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: program.color.withValues(alpha: 0.2),
            blurRadius: 30,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(32),
        child: Stack(
          children: [
            // Program content
            SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(30.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: program.color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Icon(
                            program.icon,
                            color: program.color,
                            size: 48,
                          ),
                        ),
                        const SizedBox(width: 26),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                program.title,
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF0F3A4D),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: program.color.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  program.format,
                                  style: TextStyle(
                                    color: program.color,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Focus Areas
                    _buildSection(
                      'Focus Areas',
                      program.focusAreas,
                      program.color,
                    ),
                    const SizedBox(height: 12),

                    // Program Highlights
                    _buildSection(
                      'Program Highlights',
                      program.highlights,
                      program.color,
                    ),
                    const SizedBox(height: 12),

                    // Target Audience
                    _buildSection(
                      'Target Audience',
                      program.audience,
                      program.color,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ProgramItem program = ProgramItem(
    id: 1,
    title: "Holcim Forum",
    focusAreas:
        '''Bring together people face-to-face from all regions and diverse countries, backgrounds, functions and divisions; celebrate DIVERSITY
Engage participants to BUILD SKILLS and CO-CREATE in a global environment supported by innovation experts
''',
    highlights:
        "Holcim Strategy; Innovation; Sustainability: Decarbonization; Artificial Intelligence; Diversity",
    audience:
        "Professionals and Leaders with potential to innovate and accelerate Holcim’s transformation",
    format: "Virtual",
    color: Colors.green.shade700,
    icon: Icons.people,
  );
}

class ProgramItem {
  final int id;
  final String title;
  final String focusAreas;
  final String highlights;
  final String audience;
  final String format;
  final Color color;
  final IconData icon;

  ProgramItem({
    required this.id,
    required this.title,
    required this.focusAreas,
    required this.highlights,
    required this.audience,
    required this.format,
    required this.color,
    required this.icon,
  });
}
