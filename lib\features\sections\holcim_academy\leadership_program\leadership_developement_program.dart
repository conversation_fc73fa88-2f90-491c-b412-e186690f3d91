import 'package:cilas_biskra/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class LeadershipDevelopmentScreen extends StatelessWidget {
  const LeadershipDevelopmentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        if (details.primaryVelocity! > 0) {
          context.go(
            AppRoute.holcimAcademy,
          ); // Navigate to the next screen on swipe left
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Industrial Development Programs',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w900,
                    foreground:
                        Paint()
                          ..shader = LinearGradient(
                            colors: [
                              const Color(0xFF0F3A4D),
                              const Color(0xFF0098D8),
                              const Color(0xFF97C93D),
                              const Color(0xFF1E3A8A),
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ).createShader(Rect.fromLTWH(0, 0, 400, 400)),
                  ),
                ),
                const SizedBox(height: 8),
                // Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1E3A8A),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'TALENT IS NURTURED:',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 1.2,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'INTRODUCING HOLCIM UNIVERSITY, WHERE GROWTH STARTS WITH US',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
                // Main Content
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left Column - Leadership Levels
                      Expanded(
                        flex: 5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildLevelCard(
                              'Senior Leader',
                              'Business School for Senior Leaders',
                              const Color(0xFF1E40AF),
                              context,
                            ),
                            const Spacer(),
                            _buildLevelCard(
                              'Senior Manager',
                              'Business School for Advanced Leaders I, II',
                              const Color.fromRGBO(30, 64, 175, 1),
                              context,
                            ),
                            const Spacer(),
                            // Academies Section
                            _buildLevelCard(
                              'Middle Manager',
                              'Functional Academies',
                              const Color(0xFF1E40AF),
                              context,
                            ),
                            const Spacer(),
                            _buildLevelCard(
                              'Team Leader / Individual Contributor',
                              'Business School for Emerging Leaders',
                              const Color(0xFF6B7280),
                              context,
                            ),
                            const Spacer(),
                            _buildLevelCard(
                              'Early Career',
                              'Early Career Leadership Program',
                              const Color(0xFF1E40AF),
                              context,
                            ),
                          ],
                        ),
                      ),
                      // Right Column - Additional Info
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          width: 200,
                          margin: const EdgeInsets.only(left: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Expanded(
                                child: _buildInfoCard(
                                  'Holcim Forums',
                                  Colors.grey.shade400,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildInfoCard(
                                  'Online Learning',
                                  Colors.grey.shade400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLevelCard(
    String title,
    String subtitle,
    Color color,
    BuildContext context,
  ) {
    return GestureDetector(
      onTap: () {
        context.go(
          AppRoute.holcimLeadershipCards,
          extra: subtitle,
        ); // Navigate to the next screen on swipe right
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (subtitle.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAcademyCard(String title, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        title,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, Color color) {
    return Container(
      width: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = 13
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          title,
          style: const TextStyle(
            color: Color(0xFF374151),
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
