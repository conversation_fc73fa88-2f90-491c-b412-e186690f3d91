// import 'package:cilas_biskra/core/constants.dart';
// import 'package:cilas_biskra/features/sections/holcim_academy/leadership_cards/screens/leadership_cards_body.dart';
// import 'package:flutter/material.dart';
// import 'package:go_router/go_router.dart';

// class LeadershipCards extends StatefulWidget {
//   final String program;
//   const LeadershipCards({super.key, required this.program});

//   @override
//   State<LeadershipCards> createState() => _LeadershipCardsState();
// }

// class _LeadershipCardsState extends State<LeadershipCards> {
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onHorizontalDragEnd: (details) {
//         if (details.primaryVelocity! > 0) {
//           context.go(
//             AppRoute.holcimLeadershipProgram,
//           ); // Navigate to the next screen on swipe right
//         }
//       },
//       child: Scaffold(body: LeadershipProgramsPage(program: widget.program)),
//     );
//   }
// }
