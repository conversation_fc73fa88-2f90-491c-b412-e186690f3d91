// import 'package:flutter/material.dart';
// import 'dart:math' as math;

// class CementScreen extends StatefulWidget {
//   const CementScreen({super.key});

//   @override
//   State<CementScreen> createState() => _CementScreenState();
// }

// class _CementScreenState extends State<CementScreen>
//     with TickerProviderStateMixin {
//   late AnimationController _mainController;
//   late ScrollController _scrollController;

//   bool _isExpanded = false;

//   @override
//   void initState() {
//     super.initState();
//     _scrollController = ScrollController();

//     // Simple entrance animation - only runs once when navigating to the page
//     _mainController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 800),
//     );

//     // Start the entrance animation
//     _mainController.forward();
//   }

//   @override
//   void dispose() {
//     _mainController.dispose();
//     _scrollController.dispose();
//     super.dispose();
//   }

//   void _toggleExpand() {
//     setState(() {
//       _isExpanded = !_isExpanded;
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     // Get screen size for responsive calculations
//     final screenSize = MediaQuery.of(context).size;
//     final isLargeScreen = screenSize.width > 1200;

//     // Calculate responsive values
//     final padding = isLargeScreen ? 24.0 : 16.0;
//     final fontSize = isLargeScreen ? 1.3 : 1.0;
//     final cardFontSize = isLargeScreen ? 16.0 : 12.0;
//     final titleFontSize = isLargeScreen ? 32.0 : 24.0;
//     final childAspectRatio = isLargeScreen ? 1.1 : 0.9;

//     return Scaffold(
//       body: Stack(
//         children: [
//           // Background gradient
//           Container(
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//                 colors: [Colors.grey[100]!, Colors.white],
//               ),
//             ),
//           ),
//           // Main content - with max width constraint for large screens
//           Center(
//             child: ConstrainedBox(
//               constraints: BoxConstraints(
//                 maxWidth:
//                     1800, // Prevent extreme stretching on very large screens
//               ),
//               child: SingleChildScrollView(
//                 controller: _scrollController,
//                 physics: const BouncingScrollPhysics(),
//                 child: Padding(
//                   padding: EdgeInsets.symmetric(horizontal: padding),
//                   child: Column(
//                     children: [
//                       // App Bar
//                       SafeArea(
//                         child: Container(
//                           height: isLargeScreen ? 100 : 80,
//                           padding: EdgeInsets.symmetric(horizontal: padding),
//                           child: Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Row(
//                                 children: [
//                                   Container(
//                                     padding: EdgeInsets.all(padding / 2),
//                                     decoration: BoxDecoration(
//                                       color: const Color(
//                                         0xFF8CD44A,
//                                       ).withAlpha(26), // 0.1 * 255 = 26
//                                       shape: BoxShape.circle,
//                                     ),
//                                     child: Icon(
//                                       Icons.business,
//                                       color: const Color(0xFF8CD44A),
//                                       size: isLargeScreen ? 32 : 24,
//                                     ),
//                                   ),
//                                   SizedBox(width: padding),
//                                   Text(
//                                     'Cement Industrial Academy',
//                                     style: TextStyle(
//                                       color: Colors.black87,
//                                       fontSize: titleFontSize,
//                                       fontWeight: FontWeight.bold,
//                                       letterSpacing: 0.5,
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                               IconButton(
//                                 icon: Icon(
//                                   Icons.search,
//                                   color: Colors.black54,
//                                   size: isLargeScreen ? 32 : 24,
//                                 ),
//                                 onPressed: () {},
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
      
//                       // Header section
//                       Container(
//                         margin: EdgeInsets.only(top: padding),
//                         padding: EdgeInsets.all(padding * 1.25),
//                         decoration: BoxDecoration(
//                           gradient: const LinearGradient(
//                             begin: Alignment.topLeft,
//                             end: Alignment.bottomRight,
//                             colors: [Color(0xFF8CD44A), Color(0xFF7BC142)],
//                           ),
//                           borderRadius: BorderRadius.circular(20),
//                           boxShadow: [
//                             BoxShadow(
//                               color: const Color(
//                                 0xFF8CD44A,
//                               ).withAlpha(77), // 0.3 * 255 = 77
//                               blurRadius: 15,
//                               spreadRadius: 2,
//                               offset: const Offset(0, 5),
//                             ),
//                           ],
//                         ),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Text(
//                               'Cement Program Categories',
//                               style: TextStyle(
//                                 color: Colors.white,
//                                 fontSize: isLargeScreen ? 26 : 20,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             SizedBox(height: padding / 2),
//                             Text(
//                               'Swipe to navigate between programs or tap a category to explore',
//                               style: TextStyle(
//                                 color: Colors.white.withAlpha(
//                                   230,
//                                 ), // 0.9 * 255 = 230
//                                 fontSize: isLargeScreen ? 18 : 14,
//                               ),
//                             ),
//                             SizedBox(height: padding),
//                             Row(
//                               mainAxisAlignment:
//                                   MainAxisAlignment.spaceBetween,
//                               children: [
//                                 _buildHeaderStat(
//                                   '21',
//                                   'Total Programs',
//                                   fontSize,
//                                 ),
//                                 _buildHeaderStat('6', 'Categories', fontSize),
//                                 _buildHeaderStat(
//                                   '100%',
//                                   'Completion Rate',
//                                   fontSize,
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       ),
      
//                       SizedBox(height: padding * 1.5),
      
//                       // Categories grid
//                       GridView.builder(
//                         shrinkWrap: true,
//                         physics: const NeverScrollableScrollPhysics(),
//                         gridDelegate:
//                             SliverGridDelegateWithFixedCrossAxisCount(
//                               crossAxisCount: 3,
//                               childAspectRatio: childAspectRatio,
//                               crossAxisSpacing: padding,
//                               mainAxisSpacing: padding,
//                             ),
//                         itemCount: _getCategoriesData().length,
//                         itemBuilder: (context, index) {
//                           final category = _getCategoriesData()[index];
//                           return _buildCategoryCard(
//                             title: category['title'] as String,
//                             programCount: category['programs'] as String,
//                             color: category['color'] as Color,
//                             items: category['items'] as List<String>,
//                             icon: category['icon'] as IconData,
//                             cardFontSize: cardFontSize,
//                             padding: padding,
//                             isLargeScreen: isLargeScreen,
//                           );
//                         },
//                       ),
      
//                       SizedBox(height: padding * 4),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildHeaderStat(String value, String label, double fontSizeFactor) {
//     return Column(
//       children: [
//         Text(
//           value,
//           style: TextStyle(
//             color: Colors.white,
//             fontSize: 24 * fontSizeFactor,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         Text(
//           label,
//           style: TextStyle(
//             color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
//             fontSize: 12 * fontSizeFactor,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildCategoryCard({
//     required String title,
//     required String programCount,
//     required Color color,
//     required List<String> items,
//     required IconData icon,
//     required double cardFontSize,
//     required double padding,
//     required bool isLargeScreen,
//   }) {
//     return Container(
//       decoration: BoxDecoration(
//         gradient: LinearGradient(
//           begin: Alignment.topLeft,
//           end: Alignment.bottomRight,
//           colors: [color, color.withAlpha(204)], // 0.8 * 255 = 204
//         ),
//         borderRadius: BorderRadius.circular(20),
//         boxShadow: [
//           BoxShadow(
//             color: color.withAlpha(102), // 0.4 * 255 = 102
//             blurRadius: 10,
//             spreadRadius: 1,
//             offset: const Offset(0, 5),
//           ),
//         ],
//       ),
//       child: Material(
//         color: Colors.transparent,
//         child: InkWell(
//           onTap: () {
//             _toggleExpand();
//           },
//           borderRadius: BorderRadius.circular(20),
//           child: Stack(
//             children: [
//               // Decorative background element
//               Positioned(
//                 right: -20,
//                 top: -20,
//                 child: Opacity(
//                   opacity: 0.1,
//                   child: Icon(
//                     icon,
//                     size: isLargeScreen ? 130 : 100,
//                     color: Colors.white,
//                   ),
//                 ),
//               ),

//               Padding(
//                 padding: EdgeInsets.all(padding),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Expanded(
//                           child: Text(
//                             title,
//                             style: TextStyle(
//                               color: Colors.white,
//                               fontWeight: FontWeight.bold,
//                               fontSize: isLargeScreen ? 22 : 18,
//                             ),
//                           ),
//                         ),
//                         Container(
//                           padding: EdgeInsets.all(padding / 2),
//                           decoration: BoxDecoration(
//                             color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
//                             shape: BoxShape.circle,
//                           ),
//                           child: Icon(
//                             icon,
//                             color: Colors.white,
//                             size: isLargeScreen ? 24 : 20,
//                           ),
//                         ),
//                       ],
//                     ),
//                     SizedBox(height: padding / 2),
//                     Container(
//                       padding: EdgeInsets.symmetric(
//                         horizontal: padding / 2,
//                         vertical: padding / 4,
//                       ),
//                       decoration: BoxDecoration(
//                         color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                       child: Text(
//                         programCount,
//                         style: TextStyle(
//                           color: Colors.white,
//                           fontSize: cardFontSize,
//                           fontWeight: FontWeight.w500,
//                         ),
//                       ),
//                     ),
//                     SizedBox(height: padding * 0.75),
//                     Expanded(
//                       child: Column(
//                         mainAxisSize: MainAxisSize.min,
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           for (int i = 0; i < math.min(3, items.length); i++)
//                             Padding(
//                               padding: EdgeInsets.only(bottom: padding / 4),
//                               child: Row(
//                                 children: [
//                                   Icon(
//                                     Icons.check_circle_outline,
//                                     color: Colors.white,
//                                     size: isLargeScreen ? 18 : 14,
//                                   ),
//                                   SizedBox(width: padding / 3),
//                                   Expanded(
//                                     child: Text(
//                                       items[i],
//                                       style: TextStyle(
//                                         color: Colors.white,
//                                         fontSize: cardFontSize,
//                                       ),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           if (items.length > 3)
//                             Text(
//                               '+ ${items.length - 3} more',
//                               style: TextStyle(
//                                 color: Colors.white.withAlpha(
//                                   204,
//                                 ), // 0.8 * 255 = 204
//                                 fontSize: cardFontSize,
//                                 fontStyle: FontStyle.italic,
//                               ),
//                             ),
//                         ],
//                       ),
//                     ),
//                     SizedBox(height: padding / 2),
//                     Center(
//                       child: Container(
//                         padding: EdgeInsets.symmetric(
//                           horizontal: padding * 0.75,
//                           vertical: padding * 0.375,
//                         ),
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                         child: Text(
//                           'Explore',
//                           style: TextStyle(
//                             color: color,
//                             fontSize: cardFontSize,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   List<Map<String, dynamic>> _getCategoriesData() {
//     return [
//       {
//         'title': 'PLANT\nLEADERSHIP',
//         'programs': '2 programs',
//         'color': const Color(0xFF8CD44A),
//         'items': ['Plant manager', 'Future Plant Manager'],
//         'icon': Icons.supervisor_account,
//       },
//       {
//         'title': 'PROJECT\nMANAGEMENT',
//         'programs': '3 programs',
//         'color': const Color(0xFF8CD44A),
//         'items': ['Junior PM', 'Senior PM', 'PM Director'],
//         'icon': Icons.assignment,
//       },
//       {
//         'title': 'MAINTENANCE',
//         'programs': '6 programs',
//         'color': const Color(0xFF7BC142),
//         'items': [
//           'Maintenance mngr',
//           'Preventive maintenance eng.',
//           'Automation eng.',
//           'Mechanical/Electrical\ntechnicians and eng.',
//           'Planner',
//           'Inspector',
//         ],
//         'icon': Icons.build,
//       },
//       {
//         'title': 'PROCESS &\nPRODUCTION',
//         'programs': '4 programs',
//         'color': const Color(0xFF26A9BC),
//         'items': [
//           'Production mngr',
//           'Process Optimization mngr',
//           'Process Performance &\nOperation eng.',
//           'Refractory champion',
//           'Control Room Operator',
//         ],
//         'icon': Icons.settings,
//       },
//       {
//         'title': 'QUARRY',
//         'programs': '2 programs',
//         'color': const Color(0xFF8ECDD5),
//         'items': ['Quarry manager', 'Quarry engineer', 'Geologist'],
//         'icon': Icons.landscape,
//       },
//       {
//         'title': 'QUALITY &\nENVIRONMENT',
//         'programs': '4 programs',
//         'color': const Color(0xFF1F4E79),
//         'items': [
//           'Quality eng.',
//           'Environmental eng.',
//           'Lab. operator',
//           'Quality control shift operator',
//         ],
//         'icon': Icons.eco,
//       },
//     ];
//   }
// }
