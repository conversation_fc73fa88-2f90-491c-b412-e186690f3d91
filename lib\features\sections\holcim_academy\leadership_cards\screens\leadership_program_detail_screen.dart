import 'package:flutter/material.dart';

class LeadershipProgramDetailScreen extends StatelessWidget {
  final String programId;

  const LeadershipProgramDetailScreen({super.key, required this.programId});

  @override
  Widget build(BuildContext context) {
    // Get program details based on programId
    final program = _getProgramById(programId);

    if (program == null) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withValues(alpha: 0.2),
              blurRadius: 30,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: const Center(
          child: Padding(
            padding: EdgeInsets.all(32),
            child: Text(
              'Program not found',
              style: TextStyle(fontSize: 18, color: Colors.red),
            ),
          ),
        ),
      );
    }

    // Return just the card content without Scaffold
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: program.color.withValues(alpha: 0.2),
            blurRadius: 30,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(32),
        child: Stack(
          children: [
            // Program content
            SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(30.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: program.color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Icon(
                            program.icon,
                            color: program.color,
                            size: 48,
                          ),
                        ),
                        const SizedBox(width: 26),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                program.title,
                                style: TextStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF0F3A4D),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: program.color.withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      "Program #${program.id}",
                                      style: TextStyle(
                                        color: program.color,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: program.color.withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      program.format,
                                      style: TextStyle(
                                        color: program.color,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Focus Areas
                    _buildSection(
                      'Focus Areas',
                      program.focusAreas,
                      program.color,
                    ),
                    const SizedBox(height: 12),

                    // Program Highlights
                    _buildSection(
                      'Program Highlights',
                      program.highlights,
                      program.color,
                    ),
                    const SizedBox(height: 12),

                    // Target Audience
                    _buildSection(
                      'Target Audience',
                      program.audience,
                      program.color,
                    ),
                  ],
                ),
              ),
            ),
            // Decorative corner
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: program.color.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(120),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 24, left: 24),
                  child: Align(
                    alignment: Alignment.topRight,
                    child: Icon(
                      Icons.arrow_forward,
                      color: program.color,
                      size: 32,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF0F3A4D),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withValues(alpha: 0.2)),
          ),
          child: Text(
            content,
            style: TextStyle(
              fontSize: 16,
              height: 1.5,
              color: Colors.grey.shade800,
            ),
          ),
        ),
      ],
    );
  }

  ProgramItem? _getProgramById(String id) {
    final programs = [
      ProgramItem(
        id: 4,
        title: "BUSINESS SCHOOL FOR SENIOR LEADERS",
        focusAreas:
            ''' - Leading for effective execution, driving performance and accountability, and leading change – in the new context of building a net zero future
- Develop new leadership skills that are critical for this future
- Support a positive and performance-oriented culture that builds the organization
- Expand interpersonal networks and have fun!''',
        highlights:
            "Deep dive into Engaging People, with special focus on topics such as Emotional Intelligence, Cognitive Diversity, Resilience and Change Management",
        audience: "Senior Leaders (L1-L3) nominated by their manager",
        format: "Blended",
        color: const Color(0xFF667EEA),
        icon: Icons.business_center,
      ),
      ProgramItem(
        id: 3,
        title: "EMERGING LEADERS PROGRAM",
        focusAreas: ''' - Self-awareness and personal leadership
- Leading teams and driving performance
- Leading change and innovation
- Building networks and influence''',
        highlights:
            "Focus on developing core leadership competencies through experiential learning, coaching, and peer collaboration",
        audience:
            "High-potential employees (L4-L5) identified through talent review",
        format: "Blended",
        color: const Color(0xFF4ECDC4),
        icon: Icons.trending_up,
      ),
      ProgramItem(
        id: 2,
        title: "FIRST-TIME MANAGERS PROGRAM",
        focusAreas: ''' - Transition from individual contributor to manager
- Building and leading effective teams
- Performance management and feedback
- Communication and delegation skills''',
        highlights:
            "Practical tools and frameworks for new managers to succeed in their leadership role",
        audience: "New managers (L5-L6) in their first management role",
        format: "Virtual",
        color: const Color(0xFF45B7D1),
        icon: Icons.supervisor_account,
      ),
      ProgramItem(
        id: 1,
        title: "LEADERSHIP FOUNDATIONS",
        focusAreas: ''' - Understanding leadership vs. management
- Self-awareness and emotional intelligence
- Basic communication and influence skills
- Introduction to team dynamics''',
        highlights:
            "Foundation program for developing basic leadership skills and mindset",
        audience:
            "Individual contributors (L6-L7) showing leadership potential",
        format: "Virtual",
        color: const Color(0xFF96CEB4),
        icon: Icons.foundation,
      ),
    ];
    try {
      final programIdInt = int.parse(id);
      final matchingPrograms = programs.where(
        (program) => program.id == programIdInt,
      );
      return matchingPrograms.isNotEmpty ? matchingPrograms.first : null;
    } catch (e) {
      return null;
    }
  }
}

class ProgramItem {
  final int id;
  final String title;
  final String focusAreas;
  final String highlights;
  final String audience;
  final String format;
  final Color color;
  final IconData icon;

  ProgramItem({
    required this.id,
    required this.title,
    required this.focusAreas,
    required this.highlights,
    required this.audience,
    required this.format,
    required this.color,
    required this.icon,
  });
}
