import 'package:cilas_biskra/core/constants.dart';
import 'package:cilas_biskra/features/sections/cement_academy/screens/cament_academies.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CementAcademy extends StatelessWidget {
  const CementAcademy({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! > 0) {
            context.go(AppRoute.paths);
          }
        },
        child: const CementAcademiesBody(),
      ),
    );
  }
}
