import 'package:flutter/material.dart';

class CementDetails extends StatefulWidget {
  final String? initialTab;

  const CementDetails({super.key, this.initialTab});

  @override
  State<CementDetails> createState() => _CementDetailsState();
}

class _CementDetailsState extends State<CementDetails>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final AnimationController _animationController;
  late final Animation<double> _fadeInAnimation;
  late final Animation<double> _scaleAnimation;

  final Map<String, dynamic> trainingData = {
    'Maintenance': {
      'BE READY onboarding program': {
        'forWho': 'All newcomers joining the plant team',
        'what': '''Holcim fast-track to:
                - get to know our company;
                - master the basics of the cement manufacturing;
                - ensure operational efficiency;
                - build network.''',
        'how':
            'E-learning, on the job activities, webinars, in-plant project work',
        'time': '3-month duration from hiring date',
      },
      'Certification for Inspectors / Planners': {
        'forWho': 'Inspectors and Planners',
        'what': '''Certification program to:
                - use standard parameters and operating procedures
                - improve inspection KPI
                - master condition monitoring
                - manage failure history.
                Participants need to demonstrate performance by reaching given KPIs''',
        'how': '''Program structure:
                - Entry test to assess the knowledge level
                - Foundation courses
                - Advanced courses
                - On-the-job assignment
                - Qualification exam
                - Performance demonstration
                - Certification event''',
        'time': '4 to 6 months',
      },
      'Industrial Automation Dev. program': {
        'forWho': 'Junior automation and system engineers',
        'what':
            'The program provides participants with basic knowledge on instrumentation and process control systems followed by role relevant topics such as High Level Control, TIS and OT Security.',
        'how':
            'The program is blended and balanced between self-learning, practical assignments and a face-to-face workshop at the plant.',
        'time': '6 months',
      },
      'Maintenance Engineers Dev. Program': {
        'forWho': 'Mechanical/ Maintenance/ Engineers & technicians Newcomers',
        'what':
            'It equips participants with the skills and competencies to apply the basic maintenance procedures for the main equipment in the cement plant and to use the maintenance management system.',
        'how':
            '''Blended program with on the job assignments structured in 8 cycles. Program also includes self-learning and virtual sessions
                It is a preparation for the PMEDP nomination.''',
        'time': '8 months',
      },
      'Preventive Maintenance Engineer Dev. Program': {
        'forWho': 'Mechanical, electrical, and maintenance engineers',
        'what':
            'Certification program to build skills and competencies to ensure the use of state-of-the-art techniques, guidelines and preventive and predictive maintenance practices.',
        'how':
            'Blended program including self-learning, virtual sessions, a project execution and a practical week at the plant.',
        'time': '12 months',
      },
      'Maintenance Manager Dev. Program': {
        'forWho': 'Maintenance managers',
        'what':
            'The program provides the skills, knowledge and experience required to drive maintenance performance and achieve positive business impact in line with the Country Strategy.',
        'how':
            'Blended program including self-learning, virtual sessions, a project execution and a face-to-face week.',
        'time': '8 months',
      },
    },
    'Quality & Environment': {
      'BE READY onboarding program': {
        'forWho': 'All newcomers joining the plant team',
        'what': '''Holcim fast-track to:
                - get to know our company;
                - master the basics of the cement manufacturing;
                - ensure operational efficiency;
                - build network.''',
        'how':
            'E-learning, on the job activities, webinars, in-plant project work',
        'time': '3-month duration from hiring date',
      },
      'Certification Shift Operators and Lab Operators': {
        'forWho': 'Shift Operators and Lab Operators',
        'what':
            'Certification program aim to learn and master standard parameters in order to demonstrate procedures in order to demonstrate objectives over a certain time.',
        'how': '''The certification is blended and is structured as follows:
                - Entry test to assess the knowledge
                - Foundation courses
                - Advanced courses
                - On-the-job assignment
                - Qualification exam
                - Performance demonstration
                - Certification event''',
        'time': '6-7 months depending on employee experience level',
      },
      'Quality Engineer Development Program': {
        'forWho': 'Quality engineers',
        'what':
            'Equip quality engineers with the knowledge and skills required to achieve the overall material variable and cost target, decrease CO2 footprint and ensure product compliance.',
        'how':
            'Blended program including self-learning, virtual sessions, a project execution and a one week face-to-face at the plant.',
        'time': '11 months. Next course: Nov 2025',
      },
      'Environmental Engineer Development Program': {
        'forWho': 'Environmental managers/ engineers',
        'what':
            'This specialized program focuses on environmental topics and recognizes environmental concerns in production processes. It enables participants to recognize issues within the plant team and enhance the environmental performance of the plant.',
        'how':
            'Blended program including self-study and virtual classroom sessions, business cases, and a face to face classroom training.',
        'time': '8-9 months. Next course: Mar 2025',
      },
    },
    'Quarry': {
      'Quarry Operation Development Program (QODP)': {
        'forWho':
            'N-1 level of Quarry Managers (quarry supervisors, planners and engineers, blasting supervisors), country/regional raw material managers',
        'what':
            '''Mine Planning, covering the quality & sustainability aspect from the geological model, understanding the raw mix requirements and how to create mine planning.
                Raw Material Extraction & Transport, covering the quantity and methodology aspects to extract and transport the required material from the face to the crusher in an efficient and sustainable way at low carbon footprint.
                Size Reduction, covering the fragmentation aspect: How to ensure the proper rock fragmentation from the face to the mill (drilling, blasting & crushing).''',
        'how':
            'E-learning, on the job activities, webinars, in-plant project work',
        'time': '10 months',
      },
    },
    'Process & Production': {
      'BE READY onboarding program': {
        'forWho': 'All newcomers joining the plant team',
        'what': '''Holcim fast-track to:
                - get to know our company;
                - master the basics of the cement manufacturing;
                - ensure operational efficiency;
                - build network.''',
        'how':
            'E-learning, on the job activities, webinars, in-plant project work',
        'time': '3-month duration from hiring date',
      },
      'Certification for CROs / Process Engineers Dev. Program': {
        'forWho': 'Control room operators',
        'what':
            'Certification program to use standard parameters to provide the foundation cement manufacturing processes and the basics to operate equipment, kiln, mills, fans and cooler.',
        'how': '''Program structure as follows:
                - Entry test to assess the knowledge level
                - Foundation courses
                - Advanced courses
                - On-the-job assignment
                - Qualification exam
                - Performance demonstration
                - Certification event''',
        'time': '6-7 months',
      },
      'Process Engineers Dev. Program': {
        'forWho':
            'Young process engineers, Newcomers, Production engineers and technicians',
        'what':
            'Certification program to enhance skills and competencies to optimize the operation of the cement plant and improve manufacturing processes.',
        'how':
            'Blended program with on the job assignments structured in 8 cycles. Program also includes self-learning and virtual sessions.',
        'time': '8 months. Next course: October 2024',
      },
      'Process Performance Engineers Dev. Program': {
        'forWho': 'Process engineers developed as Process Engineers',
        'what':
            'Certification program to enhance skills and competencies to optimize the operation of the cement plant and improve manufacturing processes.',
        'how':
            'Blended program including a self-learning, virtual sessions, a project execution and a practical week at the plant.',
        'time': '12 months. Next course: July 2024',
      },
      'Refractory Champion Certification Program': {
        'forWho':
            'Production manager, production supervisor or the person who is responsible for refractory at plant and country level',
        'what':
            'Provide knowledge about the selection, installation, analysis (RCA) and reporting (RMT) related to refractory activities. Additionally, it fosters good exchange amongst the plants.',
        'how':
            'Blended program including self-learning, virtual sessions, on-the-job assignments and a practical week at the plant.',
        'time': '6-7 months. Next course: April 2024',
      },
    },
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _tabController.addListener(_handleTabSelection);

    // Set initial tab based on parameter
    if (widget.initialTab != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setInitialTab(widget.initialTab!);
      });
    }

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 0.7, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _setInitialTab(String tabName) {
    final tabMap = {
      'process-production': 0,
      'quarry': 1,
      'quality-environment': 2,
      'maintenance': 3,
      'plant-leadership': 4,
      'project-management': 5,
    };

    final tabIndex = tabMap[tabName.toLowerCase()];
    if (tabIndex != null && tabIndex < _tabController.length) {
      _tabController.animateTo(tabIndex);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.blue],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: const BoxDecoration(
                          color: Color(0xFF0A5F73),
                          shape: BoxShape.circle,
                        ),
                        child: const Center(
                          child: Text(
                            'H',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Text(
                        'Cement Industrial Academy',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0A5F73),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Expanded(
              child: Center(
                child: SizedBox(
                  width: size.width * 0.85,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FadeTransition(
                        opacity: _fadeInAnimation,
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color.fromARGB(
                                  255,
                                  12,
                                  43,
                                  90,
                                ).withAlpha((0.85 * 255).round()),
                                const Color(
                                  0xFF20B2AA,
                                ).withAlpha((0.85 * 255).round()),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(
                                  (0.2 * 255).round(),
                                ),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: const Icon(
                                      Icons.supervisor_account_rounded,
                                      color: Color(0xFF0A5F73),
                                      size: 36,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  const Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Cement Industrial Academy',
                                          style: TextStyle(
                                            fontSize: 28,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 24),
                              const Text(
                                'Welcome to the Cement Industrial Academy.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(
                                (0.05 * 255).round(),
                              ),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: TabBar(
                          controller: _tabController,
                          indicator: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: const LinearGradient(
                              colors: [
                                Color.fromARGB(255, 12, 43, 90),
                                Color(0xFF20B2AA),
                              ],
                            ),
                          ),
                          labelColor: Colors.white,
                          unselectedLabelColor: const Color.fromARGB(
                            255,
                            12,
                            43,
                            90,
                          ),
                          tabs: const [
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.factory),
                                  SizedBox(width: 8),
                                  Text('Process & Production'),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.landscape),
                                  SizedBox(width: 8),
                                  Text('Quarry'),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.eco),
                                  SizedBox(width: 5),
                                  Text(
                                    'Quality & Environment',
                                    style: TextStyle(
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.build),
                                  SizedBox(width: 8),
                                  Text('Maintenance'),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.business_center),
                                  SizedBox(width: 8),
                                  Text('Plant & Leadership'),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.engineering),
                                  SizedBox(width: 8),
                                  Text('Project Management'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return TabBarView(
                              controller: _tabController,
                              children: [
                                _buildAnimatedTabContent(
                                  'Process & Production',
                                ),
                                _buildAnimatedTabContent('Quarry'),
                                _buildAnimatedTabContent(
                                  'Quality & Environment',
                                ),
                                _buildAnimatedTabContent('Maintenance'),
                                _buildPlantLeadershipContent(),
                                _buildProjectManagementContent(),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedTabContent(String category) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeInAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: TrainingTabContent(
              category: category,
              trainingData: trainingData,
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlantLeadershipContent() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeInAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header section
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF006B82), Color(0xFF20B2AA)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.business_center,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Plant Leadership Development',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Developing the next generation of plant leaders',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  // Cards section
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final isWideScreen = constraints.maxWidth > 800;

                      if (isWideScreen) {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: ProgramCard(
                                title: 'PLANT MANAGER SEMINAR',
                                subtitle: 'Turning vision into reality',
                                content: [
                                  '• How decisions impact plant performance and finances',
                                  '• Understanding levers for decarbonisation',
                                  '• Learning via shared experiences',
                                  'Target: Plant Managers (nominated)',
                                  'Contact: Alessandra Noris (Head of L&D, CEM)',
                                ],
                                color: const Color(0xFF006B82),
                                icon: Icons.account_balance,
                              ),
                            ),
                            const SizedBox(width: 32),
                            Expanded(
                              child: ProgramCard(
                                title: 'AMEA FUTURE PLANT MANAGERS',
                                subtitle: 'Development path for potential PMs',
                                content: [
                                  '• Bridge leadership, business, and technical gaps',
                                  '• Learn culture, tech, and strategy through hands-on and shadowing',
                                  '• Exposure to other plants + Six Sigma tools',
                                  'Target: N-1 level PMs (nominated)',
                                  'Contact: Arshad Tarar (L&D Program Manager)',
                                ],
                                color: const Color(0xFFE3B505),
                                icon: Icons.trending_up,
                              ),
                            ),
                          ],
                        );
                      } else {
                        return Column(
                          children: [
                            ProgramCard(
                              title: 'PLANT MANAGER SEMINAR',
                              subtitle: 'Turning vision into reality',
                              content: [
                                '• How decisions impact plant performance and finances',
                                '• Understanding levers for decarbonisation',
                                '• Learning via shared experiences',
                                'Target: Plant Managers (nominated)',
                                'Contact: Alessandra Noris (Head of L&D, CEM)',
                              ],
                              color: const Color(0xFF006B82),
                              icon: Icons.account_balance,
                            ),
                            const SizedBox(height: 24),
                            ProgramCard(
                              title: 'AMEA FUTURE PLANT MANAGERS',
                              subtitle: 'Development path for potential PMs',
                              content: [
                                '• Bridge leadership, business, and technical gaps',
                                '• Learn culture, tech, and strategy through hands-on and shadowing',
                                '• Exposure to other plants + Six Sigma tools',
                                'Target: N-1 level PMs (nominated)',
                                'Contact: Arshad Tarar (L&D Program Manager)',
                              ],
                              color: const Color(0xFFE3B505),
                              icon: Icons.trending_up,
                            ),
                          ],
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProjectManagementContent() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeInAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header section
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          Color.fromARGB(255, 12, 43, 90),
                          Color.fromARGB(255, 5, 101, 227),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.engineering,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Project Management Excellence',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Building world-class project management capabilities',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  // Cards section
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final isWideScreen = constraints.maxWidth > 1000;

                      if (isWideScreen) {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: ProgramCard(
                                title: 'Project Management Fundamentals',
                                subtitle: 'Global Program',
                                content: [
                                  '• PM fundamentals + Holcim success criteria',
                                  '• CAPM exam prep',
                                  '• For PMs with 2-3 yrs exp',
                                  '→ 6 months, 14 days commitment',
                                ],
                                color: const Color(0xFF8CC152),
                                icon: Icons.school,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ProgramCard(
                                title: 'Advanced CAPEX Project Management',
                                subtitle: 'By Region',
                                content: [
                                  '• Team + stakeholder engagement, risk & quality',
                                  '• PMP exam prep',
                                  '• For PMs with >7 yrs exp',
                                  '→ 6 months, 17 days commitment',
                                ],
                                color: const Color(0xFFE3B505),
                                icon: Icons.construction,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ProgramCard(
                                title: 'Strategic CAPEX Project Management',
                                subtitle: 'Global Program',
                                content: [
                                  '• Strategy implementation, governance, performance',
                                  '• PMP cert required + 5 yrs exp',
                                  '→ 4 months, 9 days (+5 for PMP cert)',
                                ],
                                color: const Color(0xFF006B82),
                                icon: Icons.analytics,
                              ),
                            ),
                          ],
                        );
                      } else {
                        return Column(
                          children: [
                            ProgramCard(
                              title: 'Project Management Fundamentals',
                              subtitle: 'Global Program',
                              content: [
                                '• PM fundamentals + Holcim success criteria',
                                '• CAPM exam prep',
                                '• For PMs with 2-3 yrs exp',
                                '→ 6 months, 14 days commitment',
                              ],
                              color: const Color(0xFF8CC152),
                              icon: Icons.school,
                            ),
                            const SizedBox(height: 24),
                            ProgramCard(
                              title: 'Advanced CAPEX Project Management',
                              subtitle: 'By Region',
                              content: [
                                '• Team + stakeholder engagement, risk & quality',
                                '• PMP exam prep',
                                '• For PMs with >7 yrs exp',
                                '→ 6 months, 17 days commitment',
                              ],
                              color: const Color(0xFFE3B505),
                              icon: Icons.construction,
                            ),
                            const SizedBox(height: 24),
                            ProgramCard(
                              title: 'Strategic CAPEX Project Management',
                              subtitle: 'Global Program',
                              content: [
                                '• Strategy implementation, governance, performance',
                                '• PMP cert required + 5 yrs exp',
                                '→ 4 months, 9 days (+5 for PMP cert)',
                              ],
                              color: const Color(0xFF006B82),
                              icon: Icons.analytics,
                            ),
                          ],
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class TrainingTabContent extends StatefulWidget {
  final String category;
  final Map<String, dynamic> trainingData;

  const TrainingTabContent({
    super.key,
    required this.category,
    required this.trainingData,
  });

  @override
  State<TrainingTabContent> createState() => _TrainingTabContentState();
}

class _TrainingTabContentState extends State<TrainingTabContent>
    with TickerProviderStateMixin {
  late TabController _subTabController;

  @override
  void initState() {
    super.initState();
    final programs =
        widget.trainingData[widget.category] as Map<String, dynamic>? ?? {};
    _subTabController = TabController(length: programs.length, vsync: this);
  }

  @override
  void dispose() {
    _subTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final programs =
        widget.trainingData[widget.category] as Map<String, dynamic>? ?? {};
    final subTabTitles = programs.keys.toList();
    final color = _getCategoryColor(widget.category);
    final icon = _getCategoryIcon(widget.category);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 6,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha((0.05 * 255).round()),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TabBar(
                  controller: _subTabController,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    gradient: LinearGradient(
                      colors: [color, color.withValues(alpha: 0.7)],
                    ),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: color,
                  tabs: subTabTitles.map((title) => Tab(text: title)).toList(),
                ),
                const SizedBox(height: 24),
                Expanded(
                  child: TabBarView(
                    controller: _subTabController,
                    children:
                        subTabTitles.map((title) {
                          final program =
                              programs[title] as Map<String, dynamic>;
                          return SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: color.withAlpha(
                                          (0.1 * 255).round(),
                                        ),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: Icon(icon, color: color, size: 28),
                                    ),
                                    const SizedBox(width: 16),
                                    Flexible(
                                      child: Text(
                                        title,
                                        style: TextStyle(
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          color: color,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 24),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Who it\'s for:',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      program['forWho'] ?? '',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.black54,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'What:',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      program['what'] ?? '',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.black54,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'How:',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      program['how'] ?? '',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.black54,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'Time:',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      program['time'] ?? '',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.black54,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 24),
                              ],
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Quality & Environment':
        return const Color(0xFF20B2AA);
      case 'Quarry':
        return const Color(0xFF0A5F73);
      case 'Process & Production':
        return const Color(0xFF20B2AA);
      case 'Maintenance':
        return const Color(0xFF0A5F73);
      default:
        return const Color(0xFF0A5F73);
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Quality & Environment':
        return Icons.eco;
      case 'Quarry':
        return Icons.landscape;
      case 'Process & Production':
        return Icons.factory;
      case 'Maintenance':
        return Icons.build;
      default:
        return Icons.info;
    }
  }
}

// Shared widget for program cards
class ProgramCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<String> content;
  final Color color;
  final IconData icon;

  const ProgramCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.content,
    this.color = const Color(0xFF006B82),
    this.icon = Icons.business,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, color: color, size: 28),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              ...content.map(
                (c) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 3),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (c.startsWith('•'))
                        Container(
                          margin: const EdgeInsets.only(top: 6, right: 8),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                          ),
                        ),
                      Expanded(
                        child: Text(
                          c.startsWith('•') ? c.substring(2) : c,
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                c.startsWith('Target:') ||
                                        c.startsWith('Contact:')
                                    ? color
                                    : Colors.grey.shade700,
                            fontWeight:
                                c.startsWith('Target:') ||
                                        c.startsWith('Contact:')
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
